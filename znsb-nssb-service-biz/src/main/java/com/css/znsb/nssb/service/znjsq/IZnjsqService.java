package com.css.znsb.nssb.service.znjsq;


import com.alibaba.fastjson.JSONObject;
import com.css.znsb.nssb.pojo.dto.znjsq.ZnjsqRequestDTO;
import com.css.znsb.nssb.pojo.vo.kjjyhtxxgl.ResponseVO;

import java.util.List;

public interface IZnjsqService {

    List<JSONObject> getZffylxData(ZnjsqRequestDTO rq);

    /**
     * 获取当前用户的方案列表
     *
     * @param rq
     * @return
     */
    List<JSONObject> getSchemes(ZnjsqRequestDTO rq);

    //
    //    @Override
    //    public List<JSONObject> getContract(ZnjsqRequestDTO rq) {
    //        return null;
    //    }
    //
    ResponseVO bindContract(ZnjsqRequestDTO rq);

    JSONObject getQuestions(ZnjsqRequestDTO rq);

    ResponseVO saveSchemes(ZnjsqRequestDTO rq);

    JSONObject judgeDuty(ZnjsqRequestDTO rq);

    List<JSONObject> initNsywpdfa(ZnjsqRequestDTO rq);
}
