package com.css.znsb.tzzx.service.fssrtysb.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.pojo.PageResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.framework.common.util.znsb.MD5Utils;
import com.css.znsb.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.api.nsrxx.NsrxxApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.mhzc.pojo.company.DjxhReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.JbxxmxsjVO;
import com.css.znsb.mhzc.pojo.nsrxx.SfzrdmxxxVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxReqVO;
import com.css.znsb.mhzc.pojo.nsrxx.ZnsbMhzcQyjbxxmxResVO;
import com.css.znsb.mhzc.pojo.yhxx.RyxxVO;
import com.css.znsb.nssb.constants.SfEnum;
import com.css.znsb.nssb.constants.enums.YzpzzlEnum;
import com.css.znsb.nssb.constants.enums.ZsxmEnum;
import com.css.znsb.nssb.mapper.sbrw.ZnsbNssbSbrwMapper;
import com.css.znsb.nssb.pojo.domain.sbrw.ZnsbNssbSbrwDO;
import com.css.znsb.nssb.util.DateUtil;
import com.css.znsb.nssb.utils.CxsGyUtils;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import com.css.znsb.tzzx.mapper.fssrtysb.ZnsbTzzxFssrtysbMapper;
import com.css.znsb.tzzx.pojo.domain.tzzx.fssrtysb.ZnsbTzzxFssrtysbDO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.FssrTysbGyResVO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.FssrTysbReqVO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.ZnsbTzzxFssrtysbVO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.zsxx.FssrTysbZspmVO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.zsxx.FssrTysbZszmVO;
import com.css.znsb.tzzx.service.fssrtysb.ZnsbTzzxFssrtysbService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.css.znsb.nssb.constants.SbrwztConstants.*;

/**
 * <AUTHOR>
 * @description 针对表【znsb_tzzx_fssrtysb(非税收入通用申报)】的数据库操作Service实现
 * @createDate 2025-03-26 09:19:08
 */
@Service
public class ZnsbTzzxFssrtysbServiceImpl extends ServiceImpl<ZnsbTzzxFssrtysbMapper, ZnsbTzzxFssrtysbDO>
        implements ZnsbTzzxFssrtysbService {

    @Resource
    private NsrxxApi nsrxxApi;
    @Resource
    private CompanyApi companyApi;
    @Resource
    private ZnsbTzzxFssrtysbMapper znsbTzzxFssrtysbMapper;
    @Resource
    private ZnsbTzzxFssrtysbService znsbTzzxFssrtysbService;
    @Resource
    private ZnsbNssbSbrwMapper znsbNssbSbrwMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(String nsrsbh, List<Map<String, String>> reqVO) {
        String msg = "";
        if (GyUtils.isNull(reqVO)) {
            return msg + "导入数据为空<br>";
        }
        final ZnsbMhzcQyjbxxmxReqVO nsrxxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrxxReqVO.setNsrsbh(nsrsbh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(nsrxxReqVO);
        if (GyUtils.isNull(nsrxxVO) || GyUtils.isNull(nsrxxVO.getData())) {
            return msg + "获取纳税人信息失败<br>";
        }
        final List<JbxxmxsjVO> qyjbxxList = nsrxxVO.getData().getJbxxmxsj();
        final JbxxmxsjVO qyjbxx = qyjbxxList.get(0);
        final String djxh = qyjbxx.getDjxh();
        final String nsrmc = qyjbxx.getNsrmc();
        CommonResult<CompanyBasicInfoDTO> companyResult = companyApi.basicInfo(djxh, nsrsbh);
        CompanyBasicInfoDTO companyBaseInfo = companyResult.getData();
        final String xzqhszDm = SfEnum.getSsjXzqhszDmByXzqhszDm(companyBaseInfo.getXzqhszDm());

        final String zgswskfjDm = qyjbxx.getZgswskfjDm();
        final List<SfzrdmxxxVO> sfzrdmxxx = nsrxxVO.getData().getSfzrdmxxx();
        if (GyUtils.isNull(sfzrdmxxx)) {
            return msg + "获取税费种认定信息失败<br>";
        }

        List<ZnsbTzzxFssrtysbVO> fssrtysbVOList = BeanUtils.toBean(reqVO, ZnsbTzzxFssrtysbVO.class);
        fssrtysbVOList.stream().map(o -> {
            o.setMd5uuid(MD5Utils.md5(o.getNsrsbh() + "/" + o.getSkssqq() + "/" + o.getSkssqz() + "/"
                    + o.getZsxmmc() + (GyUtils.isNotNull(o.getZspmmc()) ? "/" + o.getZspmmc() : "")));
            return o;
        }).collect(Collectors.toList());

        List<String> msgList = new ArrayList<>();
        Map<String, List<ZnsbTzzxFssrtysbVO>> drsjMap = fssrtysbVOList.stream().collect(Collectors.groupingBy(ZnsbTzzxFssrtysbVO::getMd5uuid));
        drsjMap.entrySet().stream().forEach(entry -> {
            if (entry.getValue().size() > 1) {
                fssrtysbVOList.removeAll(entry.getValue());
                msgList.add(DateUtils.dateToString(entry.getValue().get(0).getSkssqq(), 3) + "/"
                        + DateUtils.dateToString(entry.getValue().get(0).getSkssqz(), 3) + "/" +
                        entry.getValue().get(0).getZsxmmc() + (GyUtils.isNotNull(entry.getValue().get(0).getZspmmc()) ? "/" + entry.getValue().get(0).getZspmmc() + "/数据重复请检查<br>" : "/数据重复请检查<br>"));
            }
        });
        if (GyUtils.isNotNull(msgList)) {
            for (String msgString : msgList) {
                msg = msg + msgString;
            }
        }
        if (GyUtils.isNull(fssrtysbVOList)) {
            return msg;
        }
        List<ZnsbTzzxFssrtysbDO> fssrtysbDOList = BeanUtils.toBean(fssrtysbVOList, ZnsbTzzxFssrtysbDO.class);
        LocalDateTime date = LocalDateTime.now();
        List<ZnsbTzzxFssrtysbDO> fssrTysbDOListNew = new ArrayList<>();
        for (ZnsbTzzxFssrtysbDO tysb : fssrtysbDOList) {
            final Date skssqq = tysb.getSkssqq();
            final Date skssqz = tysb.getSkssqz();
            final String nsqxDm = CxsGyUtils.getNsqxDmByYfkd(skssqq, skssqz);
            final String zsxmDm = tysb.getZsxmDm();
            final String zsxmmc = tysb.getZsxmmc();
            final String zspmmc = tysb.getZspmmc();
            final String zspmDm = tysb.getZspmDm();
            final List<SfzrdmxxxVO> sfzrdxxGhjfZsxm = sfzrdmxxx.stream().filter(o -> ZsxmEnum.GHJF.getZsxmDm().equals(o.getZsxmDm()) && !skssqq.before(o.getRdyxqq())
                    && !skssqz.after(o.getRdyxqz())).collect(Collectors.toList());
            final List<SfzrdmxxxVO> sfzrdxxCzljclZsxm = sfzrdmxxx.stream().filter(o -> ZsxmEnum.JSXZSYXSFSR.getZsxmDm().equals(o.getZsxmDm()) && !skssqq.before(o.getRdyxqq())
                    && !skssqz.after(o.getRdyxqz())).collect(Collectors.toList());
            String yzpzzlDm = zsxmDm.equals(ZsxmEnum.GHJF.getZsxmDm()) ? YzpzzlEnum.TYSB.getDm() : YzpzzlEnum.FSSR_TYSB.getDm();
            tysb.setDjxh(djxh);
            tysb.setNsrmc(nsrmc);
            tysb.setYzpzzlDm(yzpzzlDm);
            tysb.setXzqhszDm(xzqhszDm);
            tysb.setZgswskfjDm(zgswskfjDm);
            tysb.setLrrq(date);
            tysb.setYxbz("Y");
            tysb.setYsbbz("N");
            tysb.setNsqxDm(nsqxDm);
            if (!skssqq.equals(skssqz)) {
                if (zsxmDm.equals(ZsxmEnum.GHJF.getZsxmDm())) {
                    yzpzzlDm = YzpzzlEnum.TYSB.getDm();
                    tysb.setYzpzzlDm(yzpzzlDm);
                    if (GyUtils.isNotNull(sfzrdxxGhjfZsxm)) {

                        final List<SfzrdmxxxVO> ghjfSfzrdZspmList = sfzrdxxGhjfZsxm.stream()
                                .filter(f -> !skssqq.before(f.getRdyxqq())
                                        && !skssqz.after(f.getRdyxqz()) && zspmDm.equals(f.getZspmDm()) && nsqxDm.equals(f.getNsqxDm()))
                                .collect(Collectors.toList());
                        if (GyUtils.isNotNull(ghjfSfzrdZspmList)) {
                            for (SfzrdmxxxVO ghjf : ghjfSfzrdZspmList) {
                                ZnsbTzzxFssrtysbDO tysbNew = BeanUtils.toBean(tysb, ZnsbTzzxFssrtysbDO.class);
                                tysbNew.setZszmDm(ghjf.getZszmDm());
                                tysbNew.setUuid(GyUtils.getUuid());
                                fssrTysbDOListNew.add(tysbNew);
                            }
                        } else {
                            msg = msg + DateUtils.dateToString(skssqq, 3) + "/" + DateUtils.dateToString(skssqz, 3) + "/" + zspmmc + "/" + "获取税费种认定信息失败<br>";
                        }

                    } else {
                        msg = msg + DateUtils.dateToString(skssqq, 3) + "/" + DateUtils.dateToString(skssqz, 3) + "/" + zsxmmc + "/" + "获取税费种认定信息失败<br>";
                    }


                } else if (zsxmDm.equals(ZsxmEnum.JSXZSYXSFSR.getZsxmDm())) {
                    yzpzzlDm = YzpzzlEnum.FSSR_TYSB.getDm();
                    tysb.setYzpzzlDm(yzpzzlDm);
                    if (GyUtils.isNotNull(sfzrdxxCzljclZsxm)) {

                        final List<SfzrdmxxxVO> czljclSfzrdZspmList = sfzrdxxCzljclZsxm.stream()
                                .filter(f -> !skssqq.before(f.getRdyxqq())
                                        && !skssqz.after(f.getRdyxqz()) && zspmDm.equals(f.getZspmDm()) && nsqxDm.equals(f.getNsqxDm()))
                                .collect(Collectors.toList());
                        if (GyUtils.isNotNull(czljclSfzrdZspmList)) {
                            for (SfzrdmxxxVO czlj : czljclSfzrdZspmList) {
                                ZnsbTzzxFssrtysbDO tysbNew = BeanUtils.toBean(tysb, ZnsbTzzxFssrtysbDO.class);
                                tysbNew.setZszmDm(czlj.getZszmDm());
                                tysbNew.setUuid(GyUtils.getUuid());
                                fssrTysbDOListNew.add(tysbNew);
                            }
                        } else {
                            msg = msg + DateUtils.dateToString(skssqq, 3) + "/" + DateUtils.dateToString(skssqz, 3) + "/" + zspmmc + "/" + "获取税费种认定信息失败<br>";
                        }

                    } else {
                        msg = msg + DateUtils.dateToString(skssqq, 3) + "/" + DateUtils.dateToString(skssqz, 3) + "/" + zsxmmc + "/" + "获取税费种认定信息失败<br>";
                    }

                }
            } else {
                tysb.setUuid(GyUtils.getUuid());
                fssrTysbDOListNew.add(tysb);
            }

        }
        List<ZnsbTzzxFssrtysbDO> zfDOList = new ArrayList<>();
        for (ZnsbTzzxFssrtysbDO fssrtysbDO : fssrTysbDOListNew) {
            if (GyUtils.isNotNull(fssrtysbDO.getZszmDm())) {
                Map<String, Object> zszmmcMap = CacheUtils.getTableData("dm_gy_zszm", fssrtysbDO.getZszmDm());
                if (GyUtils.isNotNull(zszmmcMap)) {
                    fssrtysbDO.setZszmmc(String.valueOf(zszmmcMap.get("zszmmc")));
                }
            }
            if (!fssrtysbDO.getSkssqq().equals(fssrtysbDO.getSkssqz())) {
                ZnsbTzzxFssrtysbDO tysbDO = znsbTzzxFssrtysbMapper.queryFssrTysb(fssrtysbDO.getDjxh(), fssrtysbDO.getSkssqq(), fssrtysbDO.getSkssqz(), fssrtysbDO.getZsxmDm(), fssrtysbDO.getZspmDm(), fssrtysbDO.getZszmDm());
                if (GyUtils.isNotNull(tysbDO)) {
                    ZnsbTzzxFssrtysbDO zfDO = BeanUtils.toBean(fssrtysbDO, ZnsbTzzxFssrtysbDO.class);
                    zfDO.setYxbz("N");
                    zfDO.setXgrq(date);
                    zfDO.setUuid(tysbDO.getUuid());
                    zfDOList.add(zfDO);
                }
            }
            final String sbrwuuid = znsbTzzxFssrtysbService.clsbrw(fssrtysbDO.getDjxh(), fssrtysbDO.getNsrsbh(), fssrtysbDO.getNsrmc(), fssrtysbDO.getYzpzzlDm(), fssrtysbDO.getZsxmDm(), fssrtysbDO.getZspmDm(), fssrtysbDO.getXzqhszDm(), fssrtysbDO.getNsqxDm(), fssrtysbDO.getSkssqq(), fssrtysbDO.getSkssqz());
            fssrtysbDO.setSbrwuuid(sbrwuuid);
        }
        if (GyUtils.isNotNull(zfDOList)) {
            fssrTysbDOListNew.addAll(zfDOList);
        }
        znsbTzzxFssrtysbService.saveOrUpdateBatch(fssrTysbDOListNew);
        return msg;
    }

    @Override
    public PageResult<ZnsbTzzxFssrtysbVO> initData(FssrTysbReqVO reqVO) {
        PageResult<ZnsbTzzxFssrtysbVO> resVO = new PageResult<>();
        PageResult<ZnsbTzzxFssrtysbDO> result = znsbTzzxFssrtysbMapper.queryFssrTysbList(reqVO);
        resVO = BeanUtils.toBean(result, ZnsbTzzxFssrtysbVO.class);
        return resVO;

    }

    @Override
    public CommonResult<List<FssrTysbZspmVO>> hqxlxx(FssrTysbReqVO reqVO) {
        List<FssrTysbZspmVO> zspmList = new ArrayList<>();
        final String nsrsbh = reqVO.getNsrsbh();
        final String zsxmDm = reqVO.getZsxmDm();
        final Date skssqq = DateUtils.strToDate(reqVO.getSkssqq());
        final Date skssqz = DateUtils.strToDate(reqVO.getSkssqz());
        final String nsqxDm = CxsGyUtils.getNsqxDmByYfkd(skssqq, skssqz);
        final ZnsbMhzcQyjbxxmxReqVO nsrxxReqVO = new ZnsbMhzcQyjbxxmxReqVO();
        nsrxxReqVO.setNsrsbh(nsrsbh);
        final CommonResult<ZnsbMhzcQyjbxxmxResVO> nsrxxVO = nsrxxApi.getNsrxxByNsrsbh(nsrxxReqVO);
        if (GyUtils.isNull(nsrxxVO)) {
            return CommonResult.error(-1, "获取纳税人信息失败");
        }
        final List<JbxxmxsjVO> qyjbxxList = nsrxxVO.getData().getJbxxmxsj();
        final JbxxmxsjVO qyjbxx = qyjbxxList.get(0);
        final String zgswskfjDm = qyjbxx.getZgswskfjDm();
        final List<SfzrdmxxxVO> sfzrdmxxx = nsrxxVO.getData().getSfzrdmxxx();

        if ("11".equals(nsqxDm)) {
            zspmList = getZspm(zsxmDm, zgswskfjDm, skssqq, skssqz);
            for (FssrTysbZspmVO zspmVO : zspmList) {
                List<FssrTysbZszmVO> zszmVOList = getZszm(zspmVO.getZspmDm(), zgswskfjDm, skssqq, skssqz);
                zspmVO.setZszmList(zszmVOList);
            }
        } else {
            if (ZsxmEnum.GHJF.getZsxmDm().equals(zsxmDm)) {
                if (GyUtils.isNotNull(sfzrdmxxx)) {
                    final List<SfzrdmxxxVO> sfzrdxxGhjfZsxm = sfzrdmxxx.stream().filter(o -> ZsxmEnum.GHJF.getZsxmDm().equals(o.getZsxmDm()) && !skssqq.before(o.getRdyxqq())
                            && !skssqz.after(o.getRdyxqz())).collect(Collectors.toList());
                    if (xlxxSfzrd(zspmList, skssqq, skssqz, nsqxDm, sfzrdxxGhjfZsxm)) {
                        return CommonResult.error(-1, "无税费种认定不允许新增");
                    } else {
                        return CommonResult.success(zspmList);
                    }

                } else {
                    return CommonResult.error(-1, "无税费种认定不允许新增");
                }
            } else {
                if (GyUtils.isNotNull(sfzrdmxxx)) {
                    final List<SfzrdmxxxVO> sfzrdxxCzljclZsxm = sfzrdmxxx.stream().filter(o -> ZsxmEnum.JSXZSYXSFSR.getZsxmDm().equals(o.getZsxmDm()) && !skssqq.before(o.getRdyxqq())
                            && !skssqz.after(o.getRdyxqz())).collect(Collectors.toList());
                    if (xlxxSfzrd(zspmList, skssqq, skssqz, nsqxDm, sfzrdxxCzljclZsxm)) {
                        return CommonResult.error(-1, "无税费种认定不允许新增");
                    } else {
                        return CommonResult.success(zspmList);
                    }

                }
                {
                    return CommonResult.error(-1, "无税费种认定不允许新增");
                }
            }

        }
        return CommonResult.success(zspmList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Object> saveOrUpdateTysb(ZnsbTzzxFssrtysbVO reqVO) {
        ZnsbTzzxFssrtysbDO fssrtysbDO = BeanUtils.toBean(reqVO, ZnsbTzzxFssrtysbDO.class);
        final String nsqxDm = CxsGyUtils.getNsqxDmByYfkd(fssrtysbDO.getSkssqq(), fssrtysbDO.getSkssqz());
        fssrtysbDO.setNsqxDm(nsqxDm);
        fssrtysbDO.setYsbbz("N");
        fssrtysbDO.setYzpzzlDm(reqVO.getZsxmDm().equals(ZsxmEnum.GHJF.getZsxmDm()) ? YzpzzlEnum.TYSB.getDm() : YzpzzlEnum.FSSR_TYSB.getDm());
        ZnsbTzzxFssrtysbDO tzzxFssrtysbDO = znsbTzzxFssrtysbMapper.queryFssrTysb(reqVO.getDjxh(), reqVO.getSkssqq(), reqVO.getSkssqz(), reqVO.getZsxmDm(), reqVO.getZspmDm(), reqVO.getZszmDm());
        if (!reqVO.getSkssqq().equals(reqVO.getSkssqz())) {
            if (GyUtils.isNull(reqVO.getUuid())) {
                fssrtysbDO.setUuid(GyUtils.getUuid());
                fssrtysbDO.setLrrq(LocalDateTime.now());
                if (GyUtils.isNotNull(tzzxFssrtysbDO)) {
                    return CommonResult.error(-1, "按期保存失败,同一暑期同一征收项目,征收品目,征收子目只能存在一条");
                }
            } else {
                if (GyUtils.isNotNull(tzzxFssrtysbDO) && !tzzxFssrtysbDO.getUuid().equals(fssrtysbDO.getUuid())) {
                    fssrtysbDO.setXgrq(LocalDateTime.now());
                    return CommonResult.error(-1, "按期保存失败,同一暑期同一征收项目,征收品目,征收子目只能存在一条");
                }
            }
        } else {
            if (GyUtils.isNull(reqVO.getUuid())) {
                fssrtysbDO.setUuid(GyUtils.getUuid());
                fssrtysbDO.setLrrq(LocalDateTime.now());
            } else {
                fssrtysbDO.setXgrq(LocalDateTime.now());
            }
        }
        final String sbrwuuid = znsbTzzxFssrtysbService.clsbrw(fssrtysbDO.getDjxh(), fssrtysbDO.getNsrsbh(), fssrtysbDO.getNsrmc(), fssrtysbDO.getYzpzzlDm(), fssrtysbDO.getZsxmDm(), fssrtysbDO.getZspmDm(), fssrtysbDO.getXzqhszDm(), fssrtysbDO.getNsqxDm(), fssrtysbDO.getSkssqq(), fssrtysbDO.getSkssqz());
        fssrtysbDO.setSbrwuuid(sbrwuuid);
        znsbTzzxFssrtysbService.saveOrUpdate(fssrtysbDO);
        final String oldsbrwuuid = reqVO.getSbrwuuid();
        if (!sbrwuuid.equals(oldsbrwuuid) && GyUtils.isNotNull(oldsbrwuuid)) {
            znsbTzzxFssrtysbService.clXgSbrw(oldsbrwuuid);
        }
        return CommonResult.success("成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FssrTysbGyResVO deleteTysb(FssrTysbReqVO reqVO) {
        FssrTysbGyResVO resVO = new FssrTysbGyResVO();
        ZnsbTzzxFssrtysbDO zfDO = new ZnsbTzzxFssrtysbDO();
        zfDO.setYxbz("N");
        zfDO.setXgrq(LocalDateTime.now());
        zfDO.setUuid(reqVO.getUuid());
        znsbTzzxFssrtysbMapper.updateById(zfDO);
        znsbTzzxFssrtysbService.clXgSbrw(reqVO.getSbrwuuid());
        resVO.setCode("1");
        resVO.setMsg("成功");
        return resVO;
    }

    @Override
    public void clXgSbrw(String sbrwuuid) {
        List<ZnsbTzzxFssrtysbDO> sbrwxxList = znsbTzzxFssrtysbMapper.queryFssrTysbListBysbrwuuid(sbrwuuid);
        if (GyUtils.isNull(sbrwxxList)) {
            LambdaQueryWrapperX<ZnsbNssbSbrwDO> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(ZnsbNssbSbrwDO::getSbrwuuid, sbrwuuid)
                    .eq(ZnsbNssbSbrwDO::getNsrsbztDm, "01");
            ZnsbNssbSbrwDO sbrw = znsbNssbSbrwMapper.selectOne(queryWrapperX);
            if (GyUtils.isNotNull(sbrw)) {
                final String skssqq = DateUtils.dateToString(sbrw.getSkssqq(), 3);
                final String skssqz = DateUtils.dateToString(sbrw.getSkssqz(), 3);
                if (skssqq.equals(skssqz)) {
                    znsbNssbSbrwMapper.deleteById(sbrwuuid);
                }
            }
        }
    }

    private boolean xlxxSfzrd(List<FssrTysbZspmVO> zspmList, Date skssqq, Date skssqz, String nsqxDm, List<SfzrdmxxxVO> sfzrdxxZsxm) {
        if (GyUtils.isNotNull(sfzrdxxZsxm)) {
            final Map<String, List<SfzrdmxxxVO>> sfzrdZspmMap = sfzrdxxZsxm.stream()
                    .filter(f -> !skssqq.before(f.getRdyxqq())
                            && !skssqz.after(f.getRdyxqz()) && nsqxDm.equals(f.getNsqxDm()))
                    .collect(Collectors.groupingBy(SfzrdmxxxVO::getZspmDm));
            if (GyUtils.isNotNull(sfzrdZspmMap)) {
                for (Map.Entry<String, List<SfzrdmxxxVO>> entry : sfzrdZspmMap.entrySet()) {
                    String key = entry.getKey();
                    List<SfzrdmxxxVO> values = entry.getValue();
                    FssrTysbZspmVO zspmVO = new FssrTysbZspmVO();
                    zspmVO.setZspmDm(key);
                    Map<String, Object> zspmmcMap = CacheUtils.getTableData("dm_gy_zspm", key);
                    if (GyUtils.isNotNull(zspmmcMap)) {
                        zspmVO.setZspmmc(String.valueOf(zspmmcMap.get("zspmmc")));
                    }
                    List<FssrTysbZszmVO> zszmList = new ArrayList<>();
                    for (SfzrdmxxxVO sfzrd : values) {
                        if (GyUtils.isNotNull(sfzrd.getZszmDm())) {
                            FssrTysbZszmVO zszmVO = new FssrTysbZszmVO();
                            if (GyUtils.isNotNull(sfzrd.getZszmDm())) {
                                Map<String, Object> zszmmcMap = CacheUtils.getTableData("dm_gy_zszm", sfzrd.getZszmDm());
                                if (GyUtils.isNotNull(zszmmcMap)) {
                                    zszmVO.setZszmDm(sfzrd.getZszmDm());
                                    zszmVO.setZszmmc(String.valueOf(zszmmcMap.get("zszmmc")));
                                    zszmList.add(zszmVO);
                                }
                            }
                        }
                    }
                    zspmVO.setZszmList(zszmList);
                    zspmList.add(zspmVO);
                }

            } else {
                return true;
            }
        } else {
            return true;
        }
        return false;
    }

    private List<FssrTysbZspmVO> getZspm(String zsxmDm, String zgswjgdm, Date skssqq, Date skssqz) {
        List<FssrTysbZspmVO> zspmVOList = new ArrayList<>();
        final List<String> swjgDmList = new ArrayList<>();
        List<Map<String, Object>> zspmList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());
        if (GyUtils.isNotNull(zsxmDm))

            //上溯查找
            for (String swjgDm : swjgDmList) {
                ///耗时82ms
                Map<String, Object> zspmMap = CacheUtils.getTableData("cs_gy_glb_zspm_swjg", swjgDm);
                if (GyUtils.isNotNull(zspmMap)) {
                    zspmList = JsonUtils.toMapList((String) zspmMap.get("dataList")).stream()
                            .filter(map -> zsxmDm.equals(map.get("zsxmDm")) && "Y".equals(String.valueOf(map.get("yxbz"))) && "Y".equals(String.valueOf(map.get("xybz"))) && !skssqq.before(DateUtils.strToDate((String) map.get("yxqq")))
                                    && !skssqz.after(DateUtils.strToDate((String) map.get("yxqz"))))
                            .collect(Collectors.toList());
                    if (GyUtils.isNotNull(zspmList)) {
                        break;
                    }
                }
            }

        if (GyUtils.isNotNull(zspmList)) {
            for (Map<String, Object> zspmMap : zspmList) {
                FssrTysbZspmVO zspmVO = new FssrTysbZspmVO();
                final String zspmDm = String.valueOf(zspmMap.get("zspmDm"));
                if (GyUtils.isNotNull(zspmDm)) {
                    Map<String, Object> mcMap = CacheUtils.getTableData("dm_gy_zspm", zspmDm);
                    if (GyUtils.isNotNull(mcMap)) {
                        zspmVO.setZspmDm(zspmDm);
                        zspmVO.setZspmmc(String.valueOf(mcMap.get("zspmmc")));
                    }
                    zspmVOList.add(zspmVO);
                }

            }
        }

        return zspmVOList;
    }


    private List<FssrTysbZszmVO> getZszm(String zspmDm, String zgswjgdm, Date skssqq, Date skssqz) {
        List<FssrTysbZszmVO> zszmVOList = new ArrayList<>();
        final List<String> swjgDmList = new ArrayList<>();
        List<Map<String, Object>> zspmList = new ArrayList<>();
        FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());
        if (GyUtils.isNotNull(zspmDm))

            //上溯查找
            for (String swjgDm : swjgDmList) {
                ///耗时82ms
                Map<String, Object> zspmMap = CacheUtils.getTableData("cs_gy_glb_zszm_swjg_l", swjgDm);
                if (GyUtils.isNotNull(zspmMap)) {
                    zspmList = JsonUtils.toMapList((String) zspmMap.get("dataList")).stream()
                            .filter(map -> zspmDm.equals(map.get("zspmDm")) && "Y".equals(String.valueOf(map.get("yxbz"))) && "Y".equals(String.valueOf(map.get("xybz"))) && !skssqq.before(DateUtils.strToDate((String) map.get("yxqq")))
                                    && !skssqz.after(DateUtils.strToDate((String) map.get("yxqz"))))
                            .collect(Collectors.toList());
                    if (GyUtils.isNotNull(zspmList)) {
                        break;
                    }
                }
            }

        if (GyUtils.isNotNull(zspmList)) {
            for (Map<String, Object> zspmMap : zspmList) {
                FssrTysbZszmVO zszmVO = new FssrTysbZszmVO();
                final String zszmDm = String.valueOf(zspmMap.get("zszmDm"));
                if (GyUtils.isNotNull(zszmDm)) {
                    Map<String, Object> mcMap = CacheUtils.getTableData("dm_gy_zszm", zszmDm);
                    if (GyUtils.isNotNull(mcMap)) {
                        zszmVO.setZszmDm(zszmDm);
                        zszmVO.setZszmmc(String.valueOf(mcMap.get("zszmmc")));
                        zszmVOList.add(zszmVO);
                    }
                }
            }
        }

        return zszmVOList;
    }

    @Override
    public String clsbrw(String djxh, String nsrsbh, String nsrmc, String yzpzzlDm, String zsxmDm, String zspmDm, String xzqhszDm, String nsqxDm, Date skssqq, Date skssqz) {
        String sbrwuuid = "";
        List<ZnsbNssbSbrwDO> znsbNssbSbrwDOList = znsbNssbSbrwMapper.querySbrwForFssrTz(djxh, skssqq, skssqz, yzpzzlDm, zsxmDm, zspmDm);
        if (GyUtils.isNotNull(znsbNssbSbrwDOList)) {
            if (znsbNssbSbrwDOList.stream().filter(o -> o.getNsrsbztDm().equals(SBZT_SBZ_DM) || o.getNsrsbztDm().equals(SBZT_SBCG_DM)).collect(Collectors.toList()).size() < 1) {
                for (ZnsbNssbSbrwDO sbrwDO : znsbNssbSbrwDOList) {
                    sbrwDO.setNsrsbztDm(SBZT_WSB_DM);
                    sbrwDO.setYwqdDm("lq-qyd");
                    sbrwDO.setRwztDm(RWZT_WSB_DM);
                    sbrwDO.setRwlxDm(RWLX_FS_DM);
                    LocalDateTime newtime = LocalDateTime.now();
                    sbrwDO.setXgrq(newtime);
                    sbrwDO.setSjcsdq(xzqhszDm);
                    sbrwDO.setSjgsdq(xzqhszDm);
                    sbrwDO.setZsxmDm(zsxmDm);
                    if (YzpzzlEnum.TYSB.getDm().equals(sbrwDO.getYzpzzlDm())) {
                        sbrwDO.setZspmDm(zspmDm);
                    }
                    znsbNssbSbrwMapper.updateById(sbrwDO);
                    sbrwuuid = sbrwDO.getSbrwuuid();
                }
            }


        } else {
            ZnsbNssbSbrwDO znsbNssbSbrwDO = new ZnsbNssbSbrwDO();
            znsbNssbSbrwDO.setSbrwuuid(GyUtils.getUuid());
            znsbNssbSbrwDO.setDjxh(djxh);
            znsbNssbSbrwDO.setNsrsbh(nsrsbh);
            znsbNssbSbrwDO.setNsrmc(nsrmc);
            znsbNssbSbrwDO.setYzpzzlDm(yzpzzlDm);
            znsbNssbSbrwDO.setSbny(DateUtil.doDateFormat(new Date(), "yyyyMM"));
            znsbNssbSbrwDO.setSkssqq(skssqq);
            znsbNssbSbrwDO.setSkssqz(skssqz);
            znsbNssbSbrwDO.setNsqxDm(nsqxDm);
            znsbNssbSbrwDO.setXzqhszDm(xzqhszDm);
            znsbNssbSbrwDO.setNsrsbztDm(SBZT_WSB_DM);
            znsbNssbSbrwDO.setYwqdDm("lq-qyd");
            znsbNssbSbrwDO.setRwztDm(RWZT_WSB_DM);
            znsbNssbSbrwDO.setRwlxDm(RWLX_FS_DM);
            LocalDateTime newtime = LocalDateTime.now();
            znsbNssbSbrwDO.setLrrq(newtime);
            znsbNssbSbrwDO.setXgrq(newtime);
            znsbNssbSbrwDO.setSjcsdq(xzqhszDm);
            znsbNssbSbrwDO.setSjgsdq(xzqhszDm);
            znsbNssbSbrwDO.setSjtbSj(newtime);
            znsbNssbSbrwDO.setZsxmDm(zsxmDm);
            if (YzpzzlEnum.TYSB.getDm().equals(yzpzzlDm)) {
                znsbNssbSbrwDO.setZspmDm(zspmDm);
            }
            //新增字段
            RyxxVO ryxxVO = new RyxxVO();
            CompanyBasicInfoDTO companyBasicInfoDTO = new CompanyBasicInfoDTO();
            final DjxhReqVO reqdjxhVO = new DjxhReqVO();
            reqdjxhVO.setDjxh(djxh);
            final CommonResult<List<RyxxVO>> ryxxResult = companyApi.getBsyByQy(reqdjxhVO);
            if (GyUtils.isNotNull(ryxxResult)) {
                ryxxVO = ryxxResult.getData().get(0);
            }
            final CommonResult<CompanyBasicInfoDTO> baseResult = companyApi.basicInfo(djxh, nsrsbh);
            if (GyUtils.isNotNull(baseResult)) {
                companyBasicInfoDTO = baseResult.getData();
            }
            znsbNssbSbrwDO.setBsy(ryxxVO.getZsxm1());
            znsbNssbSbrwDO.setJguuid(companyBasicInfoDTO.getJguuid());
            znsbNssbSbrwDO.setQydmz(companyBasicInfoDTO.getQydmz());
            znsbNssbSbrwMapper.insert(znsbNssbSbrwDO);
            sbrwuuid = znsbNssbSbrwDO.getSbrwuuid();
        }
        return sbrwuuid;
    }
}




