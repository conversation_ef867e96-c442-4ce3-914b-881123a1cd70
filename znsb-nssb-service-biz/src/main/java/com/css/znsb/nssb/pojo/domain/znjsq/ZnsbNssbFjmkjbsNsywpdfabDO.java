package com.css.znsb.nssb.pojo.domain.znjsq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.css.znsb.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 非居民跨境办税纳税义务判断方案表
 * @TableName znsb_nssb_fjmkjbs_nsywpdfab
 */
@TableName(value ="znsb_nssb_fjmkjbs_nsywpdfab")
@Data
public class ZnsbNssbFjmkjbsNsywpdfabDO extends BaseDO {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private BigDecimal djxh;

    /**
     * 扣缴申报编码
     */
    @TableField(value = "kjsbbm")
    private String kjsbbm;

    /**
     * 问题选项
     */
    @TableField(value = "wtxx")
    private String wtxx;

    /**
     * 方案名称
     */
    @TableField(value = "famc")
    private String famc;

    /**
     * 作废标志
     */
    @TableField(value = "zfbz_1")
    private String zfbz1;

    /**
     * 作废人代码
     */
    @TableField(value = "zfr_dm")
    private String zfrDm;

    /**
     * 作废日期
     */
    @TableField(value = "zfrq_1")
    private Date zfrq1;

    /**
     * 适用范围||用于参数表中区别纳税人端和局端配置
     */
    @TableField(value = "syfw")
    private String syfw;

}