package com.css.znsb.nssb.service.znjsq.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.css.znsb.framework.cache.utils.CacheUtils;
import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.date.DateUtils;
import com.css.znsb.framework.common.util.json.JsonUtils;
import com.css.znsb.framework.common.util.object.BeanUtils;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.mhzc.api.company.CompanyApi;
import com.css.znsb.mhzc.pojo.company.CompanyBasicInfoDTO;
import com.css.znsb.nssb.constants.ZnjsqCommConstant;
import com.css.znsb.nssb.mapper.znjsq.ZnsbNssbFjmkjbsFahtglbMapper;
import com.css.znsb.nssb.mapper.znjsq.ZnsbNssbFjmkjbsNsywpdfabMapper;
import com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsFahtglbDO;
import com.css.znsb.nssb.pojo.domain.znjsq.ZnsbNssbFjmkjbsNsywpdfabDO;
import com.css.znsb.nssb.pojo.dto.znjsq.ZnjsqRequestDTO;
import com.css.znsb.nssb.pojo.vo.kjjyhtxxgl.ResponseVO;
import com.css.znsb.nssb.service.znjsq.IZnjsqService;
import com.css.znsb.nssb.utils.FtsCxsUtils;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.zsxx.FssrTysbZspmVO;
import com.css.znsb.tzzx.pojo.vo.fssrtysb.zsxx.FssrTysbZszmVO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <pre>
 *
 * </pre>
 *
 * @version 1.00.00
 * @<NAME_EMAIL>
 * @company 方欣科技有限公司
 * @date 2023/3/16 15:04:01
 * @Class ZnjsqServiceImpl
 * @modify by
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZnjsqServiceImpl implements IZnjsqService {

    private final ZnsbNssbFjmkjbsFahtglbMapper faglhtMapper;

    private final ZnsbNssbFjmkjbsNsywpdfabMapper faxxbMapper;

    @Resource
    private CompanyApi companyApi;
//
//    private final SbSfznjsjgmxbMapper znjsjgmxbMapper;
//
//    private final SbSfznjsjgzbMapper znjsjgzbMapper;
//
//    private final SbFjmkjbsSfznjsjgmxbfbBaseMapper znjsjgmxbfbMapper;
//
//    private final WbzyJspApiClient j3cxSvcClient;

    private static final ScriptEngine JS = new ScriptEngineManager().getEngineByName("JavaScript");

    /**
     * json报文的key字段
     */
    private static final Pattern JSON_KEY_PATTERN = Pattern.compile("\"[a-zA-Z0-9_]+\":");
    private static final Pattern UNDERLINE_STR_PATTERN = Pattern.compile("\"[a-zA-Z0-9]+_[a-zA-Z0-9]+\":");

    private static final String GLB_ZSPM_JSP = "dzswj.wbzy.cs.csGyGlbZspm.cxZspmxx";
    private static final String GLB_ZSZM_JSP = "dzswj.wbzy.cs.csGyGlbZszm.cxZszmxx";


    @Override
    public List<JSONObject> getZffylxData(ZnjsqRequestDTO rq) {
        List<Map<String, Object>> allIndexData = CacheUtils.getTableData(ZnjsqCommConstant.FYLX_CACHE_NAME);
        if (GyUtils.isNotNull(rq.getKjsbbm())) {
            allIndexData = allIndexData.stream().filter(fylxxx -> rq.getKjsbbm().equals(fylxxx.get("KJSBBM"))).collect(Collectors.toList());
        }

        // 调用方法转换 key 为大写
        convertKeysToUpperCase(allIndexData);

        return JSONObject.parseArray(JSONObject.toJSONString(allIndexData), JSONObject.class);
    }

    public static void convertKeysToUpperCase(List<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            // 新建一个临时 Map，用来存放修改过的 key-value 对
            Map<String, Object> newMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                // 将 key 转换为大写，并放入新的 Map
                newMap.put(entry.getKey().toUpperCase(), entry.getValue());
            }
            // 清空原 Map，然后将新的 Map 内容放入
            map.clear();
            map.putAll(newMap);
        }
    }

    @Override
    public List<JSONObject> getSchemes(ZnjsqRequestDTO rq) {
        // 1.获取当前用户的有效非匿名方案信息集合
        List<ZnsbNssbFjmkjbsNsywpdfabDO> faxxList = new ArrayList<>();
        try {
            faxxList = faxxbMapper.selectAllByDjxh(rq.getDjxh(), rq.getKjsbbm(), rq.getYxbz());
        } catch (Exception e) {
            log.error("获取当前业务的所有方案异常：", e);
        }
        if (CollectionUtil.isEmpty(faxxList)) {
            return new ArrayList<>();
        }
        // 切换类型
        List<JSONObject> faxxResult = JSONObject.parseArray(JSONObject.toJSONString(faxxList), JSONObject.class);
        // 1.1.根据nsywpdjguuid获取所有的方案关联合同表的有效记录
        List<String> faxxbUuidList = faxxList.stream().map(ZnsbNssbFjmkjbsNsywpdfabDO::getUuid).collect(Collectors.toList());
        // 1.2.提取所有的问题选项出来，用于逻辑判断表数据的过滤
        String faWtxx = faxxList.stream().map(ZnsbNssbFjmkjbsNsywpdfabDO::getWtxx).collect(Collectors.joining(",")) + ",";
        // 2.获取合同信息，方案与合同是多对多关系，
        List<ZnsbNssbFjmkjbsFahtglbDO> faglhtListByFaxxUuids = faglhtMapper.getFaglhtListByFaxxUuids(faxxbUuidList, rq.getDjxh());

        // 3.获取费用判断逻辑表缓存
        List<Map<String, Object>> allFypdljbList = CacheUtils.getTableData(ZnjsqCommConstant.YWSSJMXX_CACHE_NAME);
        // 3.1.根据现有方案的问题选项和扣缴申报编码，过滤提取需要的判断逻辑表缓存信息
        List<Map<String, Object>> faFypdljbList = allFypdljbList.stream().filter(fypdlj -> faWtxx.contains((String) fypdlj.get("wtxx")) && rq.getKjsbbm().equals(fypdlj.get("kjsbbm"))).collect(Collectors.toList());
        // 4。获取题目答案对照信息，转成key=wtbh_zffylxDm的map
        List<JSONObject> tmDaxxList = getTmDaxx(rq.getKjsbbm());
        Map<String, JSONObject> wtbhZffylxDaxx = tmDaxxList.stream().collect(Collectors.toMap(obj -> obj.getString("wtbh") + "_" + obj.getString("zffylxDm"), Function.identity()));
        // 5.组装返回 报文
        for (int i = 0; i < faxxResult.size(); i++) {
            List<JSONObject> wtdaxxList = new ArrayList<>();
            List<JSONObject> ywxxList = new ArrayList<>();
            JSONObject faxxObj = faxxResult.get(i);
            String wtxx = (String) faxxObj.remove("wtxx");
            String daxx, zffylxDm;
            // 兼容下历史数据
            if (wtxx.contains(StrUtil.DASHED)) {
                daxx = wtxx.split(StrUtil.DASHED)[0];
                zffylxDm = wtxx.split(StrUtil.DASHED)[1];
            } else {
                daxx = wtxx;
                zffylxDm = "";
            }
            String kjsbbm = faxxObj.getString("kjsbbm");
            // 费用判断逻辑信息。存在多个的可能，则提取第一个
            // 5.1。获取合同信息，添加合同节点
            List<String> htbhList = faglhtListByFaxxUuids.stream().filter(fahtDO -> faxxObj.get("uuid").equals(fahtDO.getFjmkjbsnsywpdfabuuid())).map(ZnsbNssbFjmkjbsFahtglbDO::getXthtbh).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(htbhList)) {
                faxxObj.put("htbh", htbhList);
            }
            // 5.2.当前方案对应的费用判断逻辑表信息
            String finalZffylxDm = zffylxDm;
            List<Map<String, Object>> nowFaPdljxx = faFypdljbList.stream().filter(
                    fypdlj -> daxx.equals(fypdlj.get("wtxx")) && kjsbbm.equals(fypdlj.get("kjsbbm")) && (StrUtil.isEmpty(finalZffylxDm) || finalZffylxDm.equals(fypdlj.get("zffylxbm")))).collect(Collectors.toList());
            if (StrUtil.isEmpty(zffylxDm)) {
                zffylxDm = (String) nowFaPdljxx.get(0).get("zffylxbm");
            }
            // 5.3.组装获取答案信息节点 wtdaxxList
            // 5.3.1.解析答案信息（A01_B02...），获取对应的问题答案详细描述
            String[] das = daxx.split(StrUtil.UNDERLINE);
            for (String da : das) {
                JSONObject wtdaxx = new JSONObject();
                // 获取答案的编号（A、B....）
                String dabh = da.substring(0, 1);
                // 获取题目的顺序号
                String wtbh = "100" + da.replace(dabh, "");
                JSONObject tmxx = wtbhZffylxDaxx.get(wtbh + "_" + zffylxDm);
                JSONArray daList = tmxx.getJSONArray("damx");
                if (CollectionUtil.isEmpty(daList)) {
                    continue;
                }
                List<Object> daList1 = null;
                try {
                    daList1 = daList.stream().filter(json -> dabh.equals(((JSONObject) json).getString("dabh"))).collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("提取答案明细异常：dabh：{}, tmxx：{}, daList：{}", dabh, tmxx, daList);
                }
                JSONObject daJson = (JSONObject) daList1.get(0);
                wtdaxx.putAll(tmxx);
                wtdaxx.putAll(daJson);
                wtdaxxList.add(wtdaxx);
            }
            // 5.4.补充费用类型代码
            Map<String, Object> fylxMap = CacheUtils.getTableData(ZnjsqCommConstant.FYLX_CACHE_NAME, zffylxDm);
            faxxObj.put("wtxx", daxx);
            faxxObj.put("fylx2jDm", zffylxDm);
            faxxObj.put("fylx2jmc", fylxMap.get("zffylxmc"));
            faxxObj.put("fylx1jDm", fylxMap.get("sjzffylxbm"));
            faxxObj.put("yyfylx2jDm", fylxMap.get("yygzdzffylxbm"));
            faxxObj.put("wtdaxxList", wtdaxxList);
            faxxObj.put("yxbz", "Y".equals(faxxObj.remove("zfbz1")) ? 'N' : "Y");
            // 5.5.装业务的相关数据的节点 ywxxList
            for (int j = 0; j < nowFaPdljxx.size(); j++) {
                Map<String, Object> ywssjmxx = nowFaPdljxx.get(j);
                if (daxx.equals(ywssjmxx.get("WTXX"))) {
                    JSONObject ywssjm = new JSONObject();
                    String ywmc = CacheUtils.dm2mc("dm_gy_zsxm", (String) ywssjmxx.get("zsxmDm"));
                    ywssjm.put("zsxmDm", ywssjmxx.get("zsxmDm"));
                    ywssjm.put("zsxmmc", ywmc);
                    ywssjm.put("ywmc", ywmc);
                    ywssjm.put("zspmDm", ywssjmxx.get("zspmDm"));
                    ywssjm.put("zspmmc", CacheUtils.getTableData("dm_gy_zspm", (String) ywssjmxx.get("zspmDm")));
                    if (null != ywssjmxx.get("zszmDm")) {
                        ywssjm.put("zszmDm", ywssjmxx.get("zszmDm"));
                        ywssjm.put("zszmmc", CacheUtils.getTableData("dm_gy_zszm", (String) ywssjmxx.get("zszmDm")));
                    }
                    ywssjm.put("sl", ywssjmxx.get("sl_1"));
                    ywssjm.put("zsl", ywssjmxx.get("zsl"));
                    ywssjm.put("hdlrl", ywssjmxx.get("hdlrl"));
                    ywssjm.put("sbsdlxDm", ywssjmxx.get("sbsdlxDm"));
                    ywssjm.put("kjlxDm", ywssjmxx.get("kjlxbm"));
                    ywssjm.put("syssxdtkDm", ywssjmxx.get("syssxdtkDm"));
                    ywxxList.add(ywssjm);
                }
            }
            // 5.4.补充优惠减免信息
            faxxObj.put("yhxxList", getYhjmList(faFypdljbList, zffylxDm));
            faxxObj.put("ywxxList", ywxxList);
            faxxResult.set(i, faxxObj);
        }
        return faxxResult;
    }

    /**
     * 获取费用判断逻辑配置的减免性质数据集合
     *
     * @param faFypdljbList 费用判断逻辑配置
     * @param zffylxDm
     * @return
     */
    private List<JSONObject> getYhjmList(List<Map<String, Object>> faFypdljbList, String zffylxDm) {
        List<JSONObject> yhxxList = new ArrayList<>();
        // 优惠减免信息
        List<Map<String, Object>> allYhjmxxList = CacheUtils.getTableData(ZnjsqCommConstant.YHZC_CACHE_NAME);
        // 补充优惠信息需要优惠减免配置表，与判断逻辑表的减免性质代码对应上。判断逻辑表没有配置减免性质代码，则不带出
        // JMXZDMLB可存在多个减免性质代码，用逗号隔开
        String jmxzDmlb = faFypdljbList.stream().filter(fypdlj -> !StrUtil.isEmptyIfStr(fypdlj.get("jmxzdmlb")))
                .map(fypdlj -> (String) fypdlj.get("jmxzdmlb")).collect(Collectors.joining(",")) + ",";
        List<Map<String, Object>> fylxYhjms = allYhjmxxList.stream().filter(
                yhjm -> zffylxDm.equals(yhjm.get("zffylxbm"))
                        && (GyUtils.isNotNull(jmxzDmlb) ? jmxzDmlb.contains((String) yhjm.get("ssjmxz_dm")) : false)
        ).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fylxYhjms)) {
            for (Map<String, Object> yhxx : fylxYhjms) {
                JSONObject yhjm = new JSONObject();
                String ssjmxzDm = (String) yhxx.get("ssjmxz_dm");
                // 享受国内税收优惠  1
                List<Map<String, Object>> jmxzdzByLb1 = CacheUtils.getTableData("cs_sb_kjqysdsbg_gnsfyhxm");
                // 享受协定待遇  2
                List<Map<String, Object>> jmxzdzByLb2 = CacheUtils.getTableData("cs_sb_kjqysdsbg_syssxdtk");
                List<Map<String, Object>> mapList = null == jmxzdzByLb1 ? null : jmxzdzByLb1.stream().filter(jmxzdz -> jmxzdz.containsValue(ssjmxzDm)).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(mapList)) {
                    mapList = null == jmxzdzByLb2 ? null : jmxzdzByLb2.stream().filter(jmxzdz -> jmxzdz.containsValue(ssjmxzDm)).collect(Collectors.toList());
                }
                if (CollectionUtil.isEmpty(mapList)) {
                    continue;
                }
                yhjm.put("jmxzmc", mapList.get(0).get("ssjmxzmc"));
                yhjm.put("ssyhxm", mapList.get(0).get("ssyhxm"));
                yhjm.put("ssjmxzDm", ssjmxzDm);
                yhjm.put("jmzcwz", yhxx.get("jmzcwz"));
                yhjm.put("jmxzywmc", yhxx.get("ssjmxzmcyw"));
                yhjm.put("zsxmDm", yhxx.get("zsxmDm"));
                yhjm.put("ssyhlxDm", yhxx.get("fjmkjbsssyhlx_dm"));
                Map<String, Object> ssyhxx = CacheUtils.getTableData(ZnjsqCommConstant.SSYHLX_CACHE_NAME, (String) yhxx.get("fjmkjbsssyhlx_dm"),Map.class);
                yhjm.put("ssyhlxmc", ssyhxx.get("fjmkjbsssyhlxmc"));
                yhxxList.add(yhjm);
            }
        }
        return yhxxList;
    }

    /**
     * 组装问题答案报文
     *
     * @return
     */
    private List<JSONObject> getTmDaxx(String kjsbbm) {
        // 1.全部题目信息，包含题目编号和题目顺序
        List<Map<String, Object>> allTmxxList = CacheUtils.getTableData(ZnjsqCommConstant.TKXX_CACHE_NAME);
        // 2.全部题目答案信息
        List<Map<String, Object>> allDaxxList = CacheUtils.getTableData(ZnjsqCommConstant.TMDAXX_CACHE_NAME);

        if (GyUtils.isNotNull(kjsbbm)) {
            allTmxxList = allTmxxList.stream().filter(tmxx -> kjsbbm.equals(tmxx.get("kjsbbm"))).collect(Collectors.toList());
            allDaxxList = allDaxxList.stream().filter(tmxx -> kjsbbm.equals(tmxx.get("kjsbbm"))).collect(Collectors.toList());
        }

        List<JSONObject> result = new ArrayList<>();
        List<Map<String, Object>> finalAllDaxxList = allDaxxList;
        allTmxxList.stream().forEach(tmxxs -> {
            JSONObject tmxxObj = new JSONObject();
            String tmbh = (String) tmxxs.get("tmbh");
            tmxxObj.put("sxh", (Integer) tmxxs.get("tmsxh"));
            tmxxObj.put("wtbh", tmbh);
            tmxxObj.put("zstj", tmxxs.get("tmxstj"));
            tmxxObj.put("zffylxDm", tmxxs.get("zffylxbm"));
            JSONArray daxxs = new JSONArray();
            finalAllDaxxList.stream().forEach(daxx -> {
                JSONObject da = new JSONObject();
                if (!daxx.get("tmbh").equals(daxx.get("tmxxbh")) && tmbh.equals(daxx.get("tmbh"))) {
                    da.put("dabh", daxx.get("tmxxbh"));
                    da.put("dams", daxx.get("tmhxxnr"));
                    da.put("daywms", daxx.get("tmhxxnryw"));
                    daxxs.add(da);
                } else if (tmbh.equals(daxx.get("tmxxbh"))) {
                    tmxxObj.put("wtms", daxx.get("tmhxxnr"));
                    tmxxObj.put("wtywms", daxx.get("tmhxxnryw"));
                }
            });
            tmxxObj.put("damx", daxxs);
            result.add(tmxxObj);
        });
        return result;

    }
//
//    @Override
//    public List<JSONObject> getContract(ZnjsqRequestDTO rq) {
//        return null;
//    }
//
    @Override
    public ResponseVO bindContract(ZnjsqRequestDTO rq) {
        ResponseVO dto = new ResponseVO();
        String fabh = rq.getFabh();
        String htbh = rq.getHtbh();
        List<String> htbhList = rq.getHtbhList();
        Date now = new Date();
        String faglhtuuid = GyUtils.getUuid();
        ZnsbNssbFjmkjbsFahtglbDO faglhtDO = new ZnsbNssbFjmkjbsFahtglbDO();
        faglhtDO = BeanUtils.toBean(rq,ZnsbNssbFjmkjbsFahtglbDO.class);
        faglhtDO.setFjmkjbsnsywpdfabuuid(fabh);
        faglhtDO.setUuid(faglhtuuid);
        faglhtDO.setDjxh(new BigDecimal(rq.getDjxh()));
        faglhtDO.setFahtbdrq(now);
        faglhtDO.setYxbz("Y");
        faglhtDO.setKjsbbm(rq.getKjsbbm());
        faglhtDO.setFahtjbrq(DateUtils.strToDate(GyUtils.isNotNull(rq.getJbrq()) ? rq.getJbrq() : "9999-12-31"));
        List<ZnsbNssbFjmkjbsFahtglbDO> faglhtListByFabh = faglhtMapper.getFaglhtListByFaxxUuids(Lists.newArrayList(fabh), rq.getDjxh());
        if ("Y".equals(rq.getOnlySave()) || GyUtils.isNotNull(htbh)) {
            // 同一个合同编号，不进行重复保存
            if (CollectionUtil.isEmpty(faglhtListByFabh.stream().filter(faglht -> htbh.equals(faglht.getXthtbh())).collect(Collectors.toList()))) {
                log.info("方案：{}对合同：{}进行绑定", fabh, htbh);
                try {
                    faglhtDO.setXthtbh(htbh);
                    faglhtMapper.insert(faglhtDO);
                } catch (Exception e) {
                    log.error("将合同与方案进行绑定异常：", e);
                    dto.setBizCode("99");
                    dto.setBizMsg(e.getMessage());
                    return dto;
                }
            }
        } else {
            log.info("方案：{}需要对合同：{}进行绑定和解绑", fabh, htbhList);
            // 方案绑定合同，需要分成两个步骤。1.传递的htbh不在库中，需要保存。2.库中的数据，htbh不在传递的合同编号集合的，需要被删掉
            List<String> addList = new ArrayList<>();
            List<String> delList = new ArrayList<>();
            faglhtListByFabh.stream().forEach(faglht -> {
                String xthtbh = faglht.getXthtbh();
                if (!htbhList.contains(xthtbh)) {
                    delList.add(xthtbh);
                }
            });
            // 1.
            htbhList.stream().forEach(hb -> {
                if (CollectionUtil.isEmpty(faglhtListByFabh.stream().filter(faglht -> hb.equals(faglht.getXthtbh())).collect(Collectors.toList()))) {
                    addList.add(hb);
                }
            });
            List<String> uuids = new ArrayList<>();
            ZnsbNssbFjmkjbsFahtglbDO finalFaglhtDO = faglhtDO;
            addList.stream().forEach(add -> {
                finalFaglhtDO.setXthtbh(add);
                String uuid = GyUtils.getUuid();
                finalFaglhtDO.setUuid(uuid);
                faglhtMapper.insert(finalFaglhtDO);
                uuids.add(uuid);
            });
            // 2.
            if (CollectionUtil.isNotEmpty(delList)) {
                log.info("合同：{}需要解绑，删除方案关联合同表的相关信息", delList);
                faglhtMapper.deleteByFauuids(delList, fabh);
            }
            if (CollectionUtil.isNotEmpty(uuids)) {
                faglhtuuid = uuids.stream().map(String::toString).collect(Collectors.joining(",")) + ",";
            }
        }


        Map<String, String> collect = Stream.of(new String[][]{{"faglhtuuid", faglhtuuid}}).collect(Collectors.toMap(data -> data[0], data -> data[1]));
        dto.setBizCode("00");
        dto.setBody(JSONObject.toJSONString(collect));
        return dto;
    }

    @Override
    public JSONObject getQuestions(ZnjsqRequestDTO rq) {
        List<JSONObject> tmDaxxList = getTmDaxx(rq.getKjsbbm());
        JSONObject one = new JSONObject();
        for (int i = 0; i < tmDaxxList.size(); i++) {
            JSONObject tmDaxx = tmDaxxList.get(i);
            String fylx2jDm = tmDaxx.getString("zffylxDm");
            Map<String, Object> fylxMap = CacheUtils.getTableData(ZnjsqCommConstant.FYLX_CACHE_NAME, fylx2jDm);
            String fylx1jDm = (String) fylxMap.get("sjzffylxbm");
            JSONObject twoObj = new JSONObject();
            if (one.containsKey(fylx1jDm)) {
                twoObj = one.getJSONObject(fylx1jDm);
            }
            JSONArray two = new JSONArray();
            if (twoObj.containsKey(fylx2jDm)) {
                two = twoObj.getJSONArray(fylx2jDm);
            }
            two.add(tmDaxx);
            twoObj.put(fylx2jDm, two);
            one.put(fylx1jDm, twoObj);
        }
        return one;
    }

    @Override
    public JSONObject judgeDuty(ZnjsqRequestDTO rq) {
        JSONObject result = new JSONObject();
        String swjgDm = "";
        CommonResult<CompanyBasicInfoDTO> basicInfoDTOCommonResult = companyApi.basicInfo(rq.getDjxh(),rq.getDjxh());
        if (GyUtils.isNull(basicInfoDTOCommonResult) || GyUtils.isNull(basicInfoDTOCommonResult.getData())) {
            return result;
        }
        CompanyBasicInfoDTO basicInfoDTO = basicInfoDTOCommonResult.getData();
        swjgDm = basicInfoDTO.getZgswjDm();

        String wtxxda = rq.getTmda();
        String zffylx = rq.getFylx2jDm();
        Assert.hasLength(wtxxda, "请先选择答案！");
        Assert.state(!ZnjsqCommConstant.ZFFY_DA_NSYW_TIP_LIST.contains(zffylx + StrUtil.UNDERLINE + wtxxda), "请重新选择费用类型");

        // 1.获取非居民跨境办税费用判定逻辑表数据
        List<Map<String, Object>> allFypdljbList = CacheUtils.getTableData(ZnjsqCommConstant.YWSSJMXX_CACHE_NAME);
        // 1.1.根据问题的答案、扣缴申报 编码和支付 费用类型代码过滤获取需要的费用判断逻辑表信息
        List<Map<String, Object>> fypdljbList = allFypdljbList.stream().filter(
                e -> wtxxda.equals(e.get("wtxx")) && zffylx.equals(e.get("zffylxbm")) && rq.getKjsbbm().equals(e.get("kjsbbm"))).collect(Collectors.toList());
        // 没有答案配置直接结束流程
        if (CollectionUtil.isEmpty(fypdljbList)) {
            return result;
        }
        // 1.2.提取当前纳税义务所有的征收品目
        Set<String> zspmList = fypdljbList.stream().map(pdlj -> null == pdlj.get("zspmDm") ? "" : (String) pdlj.get("zspmDm")).collect(Collectors.toSet());
        Set<String> zszmList = fypdljbList.stream().map(pdlj -> null == pdlj.get("zszmDm") ? "" : (String) pdlj.get("zszmDm")).collect(Collectors.toSet());
        zspmList.remove("");
        zszmList.remove("");
        // 2.解析获取纳税义务判断信息
        // 2.1.获取品目子目相关信息
        Map<String, JSONObject> zspmData = getGlbZspmZszm(swjgDm, "zspm", zspmList);
        Map<String, JSONObject> zszmData = getGlbZspmZszm(swjgDm, "zszm", zszmList);
        Map<String, JSONObject> allpmZmData = getGlbZspmZszm(swjgDm, "zm_pm", zspmList);
        // 2.2.获取品目、子目的关系集合
        Map<String, Set<JSONObject>> pmzmMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(allpmZmData)) {
            allpmZmData.forEach((zm, json) -> {
                String zspmDm = json.getString("zspmDm");
                Set<JSONObject> zms = Optional.ofNullable(pmzmMap.get(zspmDm)).orElse(new HashSet<>());
                zms.add(json);
                pmzmMap.put(zspmDm, zms);
            });
        }
        // 2.2.构建返回数据
        List<JSONObject> nsywxxList = new ArrayList<>();
        Long nowMills = System.currentTimeMillis();
        fypdljbList.stream().forEach(fypdljbxx -> {
            JSONObject nsywxx = new JSONObject();
            String zsxm = (String) fypdljbxx.get("zsxmDm");
            String syssxdtklbDm = (String) fypdljbxx.get("syssxdtkDm");
            String kjlxDm = (String) fypdljbxx.get("kjlxbm");
            nsywxx.put("ywmc", ZnjsqCommConstant.YWBM_ZSXM.get(zsxm));
            nsywxx.put("kjlxDm", kjlxDm);
            nsywxx.put("kjsbbm", fypdljbxx.get("kjsbbm"));
            nsywxx.put("sfsbbz", fypdljbxx.get("sfsbbz"));
            nsywxx.put("sbsdlxDm", fypdljbxx.get("sbsdlxDm"));
            nsywxx.put("zsxmDm", fypdljbxx.get("zsxmDm"));
            nsywxx.put("zsxmmc", fypdljbxx.get("zsxmmc"));
            String zspmDm = (String) fypdljbxx.get("zspmDm");
            String zszmDm = (String) fypdljbxx.get("zszmDm");
            nsywxx.put("zspmDm", zspmDm);
            nsywxx.put("zszmDm", zszmDm);
            Map<String, Object> zspm = new HashMap<>();
            if (GyUtils.isNotNull(zspmDm)) {
                zspm = zspmData.get(zspmDm);
                if (CollectionUtil.isNotEmpty(zspm)) {
                    nsywxx.put("zspmmc", zspm.get("zspmmc"));
                } else {
                    nsywxx.put("zspmmc", CacheUtils.getTableData(ZnjsqCommConstant.ZSPM_CACHE_NAME, zspmDm).get("zspmmc"));
                }
            }
            // 印花税
            if ("10111".equals(zsxm)) {
                Set<JSONObject> zmList;
                if ("2".equals(rq.getKjsbbm()) && CollectionUtil.isNotEmpty(zspm) && CollectionUtil.isNotEmpty(zmList = pmzmMap.get(zspmDm))) {
                    // 60012，取当前品目所有子目的税率
                    Set<String> slList = new HashSet<>();
                    for (JSONObject json : zmList) {
                        if (CollectionUtil.isEmpty(json) || !json.containsKey("fdsl")) {
                            continue;
                        }
                        slList.add(json.getString("fdsl"));
                    }
                    if (CollectionUtil.isNotEmpty(slList)) {
                        nsywxx.put("sl", formatSlValue(slList));
                    }
                } else if ("0".equals(rq.getKjsbbm()) && GyUtils.isNotNull(zszmDm) && CollectionUtil.isNotEmpty(zszmData)) {
                    // 60014的子目处理
                    JSONObject zmJson = zszmData.get(zszmDm);
                    if (null != zmJson && null != zmJson.get("fdsl")) {
                        nsywxx.put("sl", formatSlValue(zszmData.get(zszmDm).get("fdsl")));
                    }
                }
            }
            if (!nsywxx.containsKey("sl")) {
                if (CollectionUtil.isNotEmpty(zspm) && !"10104".equals(zsxm)) {
                    // 没有配置子目，或者子目在库中找不到，则使用品目
                    nsywxx.put("sl", formatSlValue(zspm.get("sl")));
                }
            }
            if (null == nsywxx.get("sl")) {
                // 像文化，需求给的有问题，排列了文化的zspm也取glb_zspm，实际上没有品目的配置。
                nsywxx.put("sl", formatSlValue(fypdljbxx.get("sl1")));
            }
            nsywxx.put("zsl", fypdljbxx.get("zsl"));
            // 2.1.根据有效日期起止，添加有效标志
            nsywxx.put("yxbz", isValidPeriodByMillis(fypdljbxx, nowMills) ? "Y" : "N");
            nsywxx.put("syssxdtklbDm", syssxdtklbDm);
            if (GyUtils.isNotNull(syssxdtklbDm)) {
                nsywxx.put("syssxdtklbmc", CacheUtils.getTableData(ZnjsqCommConstant.SSXDTK_CACHE_NAME, syssxdtklbDm).get("syssxdtkmc"));
            }
            nsywxx.put("fjmkjbsfypdljbuuid", fypdljbxx.get("uuid"));
            nsywxxList.add(nsywxx);
        });
        result.put("ywxxList", nsywxxList);
        // 3.补充优惠信息需要优惠减免配置表
        result.put("yhxxList", getYhjmList(fypdljbList, rq.getFylx2jDm()));
        return result;
    }

    @Override
    public List<JSONObject> initNsywpdfa(ZnjsqRequestDTO rq) {
        return Collections.emptyList();
    }

    /**
     * 获取全局征收品目或征收子目数据
     * 使用优化后的getZspm和getZszm方法替代原来的JSP调用
     * @param swjgDm 税务机关代码
     * @param zsKey 征收类型键值（"zspm"表示征收品目，"zszm"表示征收子目，"zm_pm"表示品目子目关系）
     * @param zsList 征收品目或子目代码集合
     * @return Map<征收代码, JSONObject数据>
     */
    private Map<String, JSONObject> getGlbZspmZszm(String swjgDm, String zsKey, Set<String> zsList) {
        if (CollectionUtil.isEmpty(zsList)) {
            return new HashMap<>();
        }

        try {
            // 根据zsKey调用相应的方法
            if ("zspm".equals(zsKey)) {
                // 调用优化后的getZspm方法
                return getZspm(zsList, swjgDm);
            } else if ("zszm".equals(zsKey) || "zm_pm".equals(zsKey)) {
                // 调用优化后的getZszm方法
                return getZszm(zsKey, zsList, swjgDm);
            } else {
                log.warn("未知的zsKey类型: {}", zsKey);
                return new HashMap<>();
            }

        } catch (Exception e) {
            log.error("getGlbZspmZszm调用异常: swjgDm={}, zsKey={}, zsList={}", swjgDm, zsKey, zsList, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取征收品目数据，返回Map<String, JSONObject>格式以符合getGlbZspmZszm的逻辑
     * @param zspmDmList 征收品目代码列表
     * @param zgswjgdm 主管税务机关代码
     * @return Map<征收品目代码, JSONObject数据>
     */
    private Map<String, JSONObject> getZspm(Set<String> zspmDmList, String zgswjgdm) {
        Map<String, JSONObject> result = new HashMap<>();

        if (CollectionUtil.isEmpty(zspmDmList)) {
            return result;
        }

        try {
            final List<String> swjgDmList = new ArrayList<>();
            FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());
            Date now = DateUtils.toDate(LocalDate.now());

            // 上溯查找有效数据
            for (String swjgDm : swjgDmList) {
                Map<String, Object> zspmMap = CacheUtils.getTableData("cs_gy_glb_zspm_swjg", swjgDm);
                if (GyUtils.isNotNull(zspmMap)) {
                    List<Map<String, Object>> zspmList = JsonUtils.toMapList((String) zspmMap.get("dataList"));
                    if (GyUtils.isNotNull(zspmList)) {
                        List<Map<String, Object>> filteredList = zspmList.stream()
                                .filter(map -> {
                                    // 检查代码匹配
                                    boolean codeMatch = zspmDmList.contains(map.get("zspmDm"));
                                    // 检查有效标志
                                    boolean validFlag = "Y".equals(String.valueOf(map.get("yxbz"))) &&
                                                       "Y".equals(String.valueOf(map.get("xybz")));
                                    // 检查有效期
                                    boolean validPeriod = isValidPeriod(map, now);

                                    return codeMatch && validFlag && validPeriod;
                                })
                                .collect(Collectors.toList());

                        if (GyUtils.isNotNull(filteredList) && !filteredList.isEmpty()) {
                            // 转换为JSONObject格式，参考getGlbZspmZszm方法的逻辑
                            for (Map<String, Object> dataMap : filteredList) {
                                JSONObject jsonObject = new JSONObject();
                                for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                    jsonObject.put(entry.getKey(), entry.getValue());
                                }

                                String zspmDm = String.valueOf(dataMap.get("zspmDm"));
                                if (GyUtils.isNotNull(zspmDm)) {
                                    // 如果已经存在相同key的数据，跳过（只取第一条）
                                    JSONObject existingData = result.get(zspmDm);
                                    if (CollectionUtil.isNotEmpty(existingData)) {
                                        continue;
                                    }
                                    result.put(zspmDm, jsonObject);
                                }
                            }
                            break; // 找到数据后停止上溯
                        }
                    }
                }
            }

            // 为结果中的每个征收品目添加名称信息
            for (Map.Entry<String, JSONObject> entry : result.entrySet()) {
                String zspmDm = entry.getKey();
                JSONObject jsonObject = entry.getValue();

                // 获取征收品目名称
                Map<String, Object> mcMap = CacheUtils.getTableData("dm_gy_zspm", zspmDm);
                if (GyUtils.isNotNull(mcMap)) {
                    jsonObject.put("zspmmc", mcMap.get("zspmmc"));
                }
            }

            if (log.isInfoEnabled()) {
                log.info("getZspm查询结果: zgswjgdm={}, 查询条件={}, 结果数量={}",
                        zgswjgdm, zspmDmList, result.size());
            }

        } catch (Exception e) {
            log.error("getZspm查询异常: zgswjgdm={}, zspmDmList={}", zgswjgdm, zspmDmList, e);
        }

        return result;
    }


    /**
     * 获取征收子目数据，返回Map<String, JSONObject>格式以符合getGlbZspmZszm的逻辑
     * @param zsKey 征收类型键值（"zszm"表示征收子目）
     * @param zsList 征收代码列表
     * @param zgswjgdm 主管税务机关代码
     * @return Map<征收代码, JSONObject数据>
     */
    private Map<String, JSONObject> getZszm(String zsKey, Set<String> zsList, String zgswjgdm) {
        Map<String, JSONObject> result = new HashMap<>();

        if (CollectionUtil.isEmpty(zsList)) {
            return result;
        }

        try {
            final List<String> swjgDmList = new ArrayList<>();
            FtsCxsUtils.getSwjgList(swjgDmList, zgswjgdm, new HashMap<>());
            Date now = DateUtils.toDate(LocalDate.now());

            // 根据zsKey确定过滤字段
            String filterField = "zszm".equals(zsKey) ? "zszmDm" : "zspmDm";

            // 上溯查找有效数据
            for (String swjgDm : swjgDmList) {
                Map<String, Object> zszmMap = CacheUtils.getTableData("cs_gy_glb_zszm_swjg_l", swjgDm);
                if (GyUtils.isNotNull(zszmMap)) {
                    List<Map<String, Object>> zszmList = JsonUtils.toMapList((String) zszmMap.get("dataList"));
                    if (GyUtils.isNotNull(zszmList)) {
                        List<Map<String, Object>> filteredList = zszmList.stream()
                                .filter(map -> {
                                    // 检查代码匹配
                                    boolean codeMatch = zsList.contains(map.get(filterField));
                                    // 检查有效标志
                                    boolean validFlag = "Y".equals(String.valueOf(map.get("yxbz"))) &&
                                                       "Y".equals(String.valueOf(map.get("xybz")));
                                    // 检查有效期
                                    boolean validPeriod = isValidPeriod(map, now);

                                    return codeMatch && validFlag && validPeriod;
                                })
                                .collect(Collectors.toList());

                        if (GyUtils.isNotNull(filteredList) && !filteredList.isEmpty()) {
                            // 转换为JSONObject格式，参考getGlbZspmZszm方法的逻辑
                            for (Map<String, Object> dataMap : filteredList) {
                                JSONObject jsonObject = new JSONObject();
                                for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                    jsonObject.put(entry.getKey(), entry.getValue());
                                }

                                String keyValue = String.valueOf(dataMap.get(filterField));
                                if (GyUtils.isNotNull(keyValue)) {
                                    // 如果已经存在相同key的数据，跳过（只取第一条）
                                    JSONObject existingData = result.get(keyValue);
                                    if (CollectionUtil.isNotEmpty(existingData)) {
                                        continue;
                                    }
                                    result.put(keyValue, jsonObject);
                                }
                            }
                            break; // 找到数据后停止上溯
                        }
                    }
                }
            }

            // 为结果中的每个征收子目添加名称信息
            for (Map.Entry<String, JSONObject> entry : result.entrySet()) {
                String zszmDm = entry.getKey();
                JSONObject jsonObject = entry.getValue();

                // 获取征收子目名称
                Map<String, Object> mcMap = CacheUtils.getTableData("dm_gy_zszm", zszmDm);
                if (GyUtils.isNotNull(mcMap)) {
                    jsonObject.put("zszmmc", mcMap.get("zszmmc"));
                }
            }

            if (log.isInfoEnabled()) {
                log.info("getZszm查询结果: zgswjgdm={}, zsKey={}, 查询条件={}, 结果数量={}",
                        zgswjgdm, zsKey, zsList, result.size());
            }

        } catch (Exception e) {
            log.error("getZszm查询异常: zgswjgdm={}, zsKey={}, zsList={}", zgswjgdm, zsKey, zsList, e);
        }

        return result;
    }

    /**
     * 验证数据的有效期是否在当前时间范围内
     * @param dataMap 数据Map，包含yxqq（有效期起）和yxqz（有效期止）字段
     * @param now 当前时间
     * @return 是否在有效期内
     */
    private boolean isValidPeriod(Map<String, Object> dataMap, Date now) {
        try {
            String yxqq = (String) dataMap.get("yxqq");
            String yxqz = (String) dataMap.get("yxqz");

            if (GyUtils.isNull(yxqq) || GyUtils.isNull(yxqz)) {
                return true; // 如果没有有效期限制，则认为有效
            }

            Date startDate = DateUtils.strToDate(yxqq);
            Date endDate = DateUtils.strToDate(yxqz);

            // 当前时间应该在有效期起止时间之间（包含边界）
            return !now.before(startDate) && !now.after(endDate);

        } catch (Exception e) {
            log.warn("日期解析异常，数据: {}, 错误: {}", dataMap, e.getMessage());
            return false; // 日期解析失败时认为无效
        }
    }

    /**
     * 使用毫秒时间戳验证数据的有效期是否在当前时间范围内
     * 支持多种数据类型的时间字段（Long、String、Date等）
     * @param dataMap 数据Map，包含yxqq（有效期起）和yxqz（有效期止）字段
     * @param nowMills 当前时间的毫秒时间戳
     * @return 是否在有效期内
     */
    private boolean isValidPeriodByMillis(Map<String, Object> dataMap, Long nowMills) {
        try {
            Object yxqqObj = dataMap.get("yxqq");
            Object yxqzObj = dataMap.get("yxqz");

            if (GyUtils.isNull(yxqqObj) || GyUtils.isNull(yxqzObj)) {
                return true; // 如果没有有效期限制，则认为有效
            }

            Long startMills = convertToMillis(yxqqObj);
            Long endMills = convertToMillis(yxqzObj);

            if (startMills == null || endMills == null) {
                return false; // 时间转换失败时认为无效
            }

            // 当前时间应该在有效期起止时间之间（包含边界）
            return nowMills >= startMills && nowMills <= endMills;

        } catch (Exception e) {
            log.warn("毫秒时间验证异常，数据: {}, 错误: {}", dataMap, e.getMessage());
            return false; // 验证失败时认为无效
        }
    }

    /**
     * 将各种类型的时间对象转换为毫秒时间戳
     * @param timeObj 时间对象（可能是Long、String、Date等类型）
     * @return 毫秒时间戳，转换失败时返回null
     */
    private Long convertToMillis(Object timeObj) {
        if (timeObj == null) {
            return null;
        }

        try {
            // 如果已经是Long类型，直接返回
            if (timeObj instanceof Long) {
                return (Long) timeObj;
            }

            // 如果是Integer类型，转换为Long
            if (timeObj instanceof Integer) {
                return ((Integer) timeObj).longValue();
            }

            // 如果是String类型，尝试解析
            if (timeObj instanceof String) {
                String timeStr = (String) timeObj;

                // 尝试直接解析为Long（毫秒时间戳）
                try {
                    return Long.parseLong(timeStr);
                } catch (NumberFormatException e) {
                    // 如果不是纯数字，尝试作为日期字符串解析
                    Date date = DateUtils.strToDate(timeStr);
                    return date != null ? date.getTime() : null;
                }
            }

            // 如果是Date类型，转换为毫秒
            if (timeObj instanceof Date) {
                return ((Date) timeObj).getTime();
            }

            // 其他类型尝试转换为字符串后再处理
            return convertToMillis(timeObj.toString());

        } catch (Exception e) {
            log.warn("时间对象转换失败: {}, 错误: {}", timeObj, e.getMessage());
            return null;
        }
    }

    private String double2Str(Object data) {
        if (null == data) {
            return "";
        }
        if (data instanceof String) {
            return (String) data;
        }
        BigDecimal b = new BigDecimal(String.valueOf(data));
        return b.toString();
    }

    /**
     * 处理税率值，确保多个值时是数组形式且不使用科学计数法
     * @param data 税率数据
     * @return 处理后的税率值（单个值返回字符串，多个值返回数组）
     */
    private Object formatSlValue(Object data) {
        if (null == data) {
            return "";
        }

        // 如果已经是集合类型，转换为字符串数组
        if (data instanceof Set) {
            Set<?> slSet = (Set<?>) data;
            List<String> slList = new ArrayList<>();
            for (Object sl : slSet) {
                slList.add(double2Str(sl));
            }
            return slList.toArray(new String[0]);
        }

        // 单个值直接转换为字符串
        return double2Str(data);
    }

    @Override
    public ResponseVO saveSchemes(ZnjsqRequestDTO rq) {
        String fabh = GyUtils.getUuid();
        String zffylxbm = rq.getFylx2jDm();
        Map<String, String> collect = Stream.of(new String[][]{{"fabh", fabh}}).collect(Collectors.toMap(data -> data[0], data -> data[1]));
        ResponseVO dto = new ResponseVO();
        ZnsbNssbFjmkjbsNsywpdfabDO faxxDO = new ZnsbNssbFjmkjbsNsywpdfabDO();
        try {
            // 1.保存方案信息表
            faxxDO.setDjxh(new BigDecimal(rq.getDjxh()));
            faxxDO.setUuid(fabh);
            faxxDO.setKjsbbm(rq.getKjsbbm());
            faxxDO.setFamc(rq.getFamc());
            faxxDO.setFamc(rq.getFamc());
            faxxDO.setSyfw(rq.getFasyfw());
            faxxDO.setWtxx(rq.getWtxx() + (GyUtils.isNotNull(zffylxbm) ? StrUtil.DASHED + zffylxbm : ""));
            BeanUtils.copyBean(rq,faxxDO);
            faxxbMapper.insert(faxxDO);
            // 2.存在合同信息时，保存方案关联合同表
            if (GyUtils.isNotNull(rq.getHtbh())) {
                rq.setFabh(fabh);
                rq.setOnlySave("Y");
                ResponseVO bindContract = bindContract(rq);
                if ("00".equals(bindContract.getBizCode())) {
                    String faglhtuuid = (String) JSONPath.read(bindContract.getBody(), "$.faglhtuuid");
                    collect.put("faglhtuuid", faglhtuuid);
                }
            }
            dto.setBizCode("00");
            dto.setBody(JSONObject.toJSONString(collect));
        } catch (Exception e) {
            log.error("使用参数{}保存方案对象：{}时异常{}", rq, faxxDO, e);
            dto.setBizCode("01");
            dto.setBizMsg(e.getMessage());
        }
        return dto;
    }
//
//
//    @Override
//    public JSONObject getTaxTreaty(ZnjsqRequestDTO rq) {
//        // 1.获取所有税收协定数据
//        List<Map<String, Object>> allSsxdxxList = CacheUtils.getTableData(ZnjsqCommConstant.SSXD_CACHE_NAME);
//        if (GyUtils.isNotNull(rq.getKjsbbm())) {
//            allSsxdxxList = allSsxdxxList.stream().filter(ssxdxx -> rq.getKjsbbm().equals(ssxdxx.get("KJSBBM"))).collect(Collectors.toList());
//        }
//        // 1.1.获取税收协定条款类别代码表数据
//        List<Map<String, Object>> allSsxdtklbxxData = CacheUtils.getTableData(ZnjsqCommConstant.SSXDTK_CACHE_NAME);
//        // 1.2.将税收协定数据根据国家地区代码分组。
//        Map<String, List<Map<String, Object>>> ssxdxxMapByGjGroup = allSsxdxxList.stream().collect(Collectors.groupingBy(e -> (String) e.get("GJHDQSZ_DM")));
//        // 1.3.将税收协定条款类别代码转换成map，便于取值
//        Map<Object, Map<String, Object>> ssxdtklbMap = allSsxdtklbxxData.stream().collect(Collectors.toMap(e -> e.get("syssxdtkDm"), Function.identity()));
//        // 2.缓存中的key字段都是大写，需要切换，JSON转string
//        String ssxdxxStr = JSONObject.toJSONString(ssxdxxMapByGjGroup);
//        // 2.1.key转小写
//        Matcher keyMatch = JSON_KEY_PATTERN.matcher(ssxdxxStr);
//        while (keyMatch.find()) {
//            String key = keyMatch.group();
//            ssxdxxStr = ssxdxxStr.replace(key, key.toLowerCase());
//        }
//        // 2.2._d之类的数据转换成D。
//        Matcher underLintMatch = UNDERLINE_STR_PATTERN.matcher(ssxdxxStr);
//        while (underLintMatch.find()) {
//            String line = underLintMatch.group();
//            String[] lines = line.split(StrUtil.UNDERLINE);
//            String keyVal = "";
//            for (int i = 0; i < lines.length; i++) {
//                if (i == 0) {
//                    keyVal = lines[0];
//                } else {
//                    keyVal += StringUtils.capitalize(lines[i]);
//                }
//            }
//            ssxdxxStr = ssxdxxStr.replace(line, keyVal);
//        }
//        JSONObject result = JSONObject.parseObject(ssxdxxStr);
//        // 3.补充协定条款类别名称
//        result.forEach((key, val) -> {
//            List<Map<String, Object>> list = (List<Map<String, Object>>) val;
//            for (int i = 0; i < list.size(); i++) {
//                Map<String, Object> map = list.get(i);
//                String ssxdtklbDm = (String) map.get("syssxdtkDm");
//                map.put("syssxdtklbmc", ssxdtklbMap.get(ssxdtklbDm).get("SYSSXDTKMC"));
//                list.set(i, map);
//            }
//            result.put(key, list);
//        });
//        JSONObject resp = new JSONObject();
//        resp.put("ssxdxx", result);
//        List<Map<String, Object>> ssxdDmCache = CacheUtils.getTableData(ZnjsqCommConstant.SSXDDM_CACHE_NAME);
//        JSONObject ssxdbxx = new JSONObject();
//        ssxdDmCache.stream().forEach(ssxd -> {
//            String gjdqDm = (String) ssxd.get("GJHDQSZ_DM");
//            JSONArray ssxdb = ssxdbxx.getJSONArray(gjdqDm);
//            if (CollectionUtil.isEmpty(ssxdb)) {
//                ssxdb = new JSONArray();
//            }
//            if (result.containsKey(gjdqDm)) {
//                ssxdb.add(ssxd);
//            }
//            if (CollectionUtil.isNotEmpty(ssxdb)) {
//                ssxdbxx.put(gjdqDm, ssxdb);
//            }
//        });
//        resp.put("ssxdb", ssxdbxx);
//        return resp;
//    }
//
//    @Override
//    public List<JSONObject> calcTax(ZnjsqRequestDTO rq) {
//        StopWatch sw = new StopWatch();
//        // 判断当前费用类型是1-49项还是50股权，1-49项则为true
//        String fylx2jDm = rq.getFylx2jDm();
//        boolean f50gqBz = Integer.parseInt(rq.getFylx1jDm()) < 23000;
//        Double htje = rq.getHtje();
//        JSONObject qqbw = JSONObject.parseObject(rq.getQqbw());
//        List<JSONObject> ywmxxx = qqbw.getObject("ywmxxx", List.class);
//        Map<String, JSONObject> zsxmYwmx = ywmxxx.stream().collect(Collectors.toMap(e -> e.getString("zsxmDm"), Function.identity()));
//        JSONObject zzsJson = zsxmYwmx.get("10101");
//        JSONObject qysdsJson = zsxmYwmx.get("10104");
//        boolean hadZzs = CollectionUtil.isNotEmpty(zzsJson);
//        boolean hadQysds = CollectionUtil.isNotEmpty(qysdsJson);
//        // 获取增值税税率、企业所得税税率，存在记录则为1，不存在则为0；f1：增值税，f2：企业所得税
//        Integer f1 = hadZzs ? 1 : 0;
//        Integer f2 = hadQysds ? 1 : 0;
//        Integer f3 = !hadZzs ? 0 : zzsJson.getInteger("sfcdlx");
//        Integer f4 = !hadQysds ? 0 : qysdsJson.getInteger("sfcdlx");
//        // 配置的增值税税率
//        Double zzsSl = hadZzs ? zzsJson.getDouble(ZnjsqCommConstant.SL_STR) : 0;
//        // 配置的企业所得税税率
//        Double qysdsSl = hadQysds ? qysdsJson.getDouble(ZnjsqCommConstant.SL_STR) : 0;
//        // 计税价款：被除以，分母
//        Double beDivided = (1 + zzsSl * f1 * (1 - f3) - qysdsSl * f2 * f4);
//        Double zfje = qqbw.getDoubleValue(ZnjsqCommConstant.ZFJE_STR);
//        Double sbsrje = qqbw.getDoubleValue(ZnjsqCommConstant.SBSRJE_STR);
//        Double tzcb = qqbw.getDoubleValue("tzcb");
//        log.info("f50gqBz:{}, f1:{}, f2:{}, f3:{}, f4:{}, zzsSl:{}，qysdsSl:{}，beDivided:{}", f50gqBz, f1, f2, f3, f4, zzsSl, qysdsSl, beDivided);
//        List<JSONObject> result = new ArrayList<>();
//        ywmxxx.stream().forEach(ywmx -> {
//            String zsxmDm = ywmx.getString("zsxmDm");
//            Double jsjk = 0.0;
//            Double hl = ywmx.getDoubleValue(ZnjsqCommConstant.HL_STR);
//            // 1.计算计税价款
//            if (f50gqBz) {
//                // todo:使用qysdsSl 或 实际征收率：指定扣缴时使用，为25%乘以核定利润率，核定利润率手动输入
//                jsjk = htje / beDivided;
//            } else {
//                // 50股权模式
//                if ("10104".equals(zsxmDm)) {
//                    Double gqjz = ywmx.getDoubleValue("gqjz");
//                    jsjk = (htje * hl - gqjz) / beDivided;
//                } else if ("10101".equals(zsxmDm)) {
//                    Double mrj = ywmx.getDouble(ZnjsqCommConstant.MRJ_STR);
//                    Double mcj = ywmx.getDouble(ZnjsqCommConstant.MCJ_STR);
//                    jsjk = (mcj - mrj) / beDivided;
//                }
//            }
//            JSONObject jsjg = new JSONObject();
//            // 2.计算税费
//            Map<String, Map<String, Object>> jsEnum = ZnjsqSfJsEnum.getJsEnum(zsxmDm, f50gqBz, rq.getYwlb());
//
//            try {
//                for (Map.Entry<String, Map<String, Object>> e : jsEnum.entrySet()) {
//                    Map<String, Object> jspz = e.getValue();
//                    String formula = (String) jspz.get("formula");
//                    String resultKey = (String) jspz.get("resultKey");
//                    Integer sx = (Integer) jspz.get("sx");
//                    sw.start();
//                    CompiledScript compile = ((Compilable) JS).compile(formula);
//                    Bindings binding = JS.createBindings();
//                    binding.put(ZnjsqCommConstant.JSJK_STR, jsjk);
//                    binding.put(ZnjsqCommConstant.HTJE_STR, htje);
//                    binding.put(ZnjsqCommConstant.HL_STR, hl);
//                    binding.put(ZnjsqCommConstant.SBSRJE_STR, sbsrje);
//                    binding.put(ZnjsqCommConstant.SL_STR, null == ywmx.get(ZnjsqCommConstant.SL_STR) ? 0.0 : ywmx.getDouble(ZnjsqCommConstant.SL_STR));
//                    binding.put("tzcb", tzcb);
//                    binding.put("f1", f1);
//                    binding.put("f2", f2);
//                    binding.put("f3", f3);
//                    binding.put("f4", f4);
//                    binding.put(ZnjsqCommConstant.ZFJE_STR, zfje);
//                    // 后续的计算依赖前面的计算结果，需要存储起来
//                    if (sx > 0) {
//                        binding.putAll(jsjg);
//                    }
//                    Object eval = compile.eval(binding);
//                    sw.stop();
//                    log.info("zsxmDm:{}}计算:{}的公式：{}}耗时：{}", zsxmDm, resultKey, formula, sw.getLastTaskTimeMillis());
//                    jsjg.put(resultKey, (Double) eval);
//                }
//            } catch (ScriptException e) {
//                log.error("计算公式编译异常：", e);
//                throw new BizNestedException("计算公式编译异常");
//            }
//            if (CollectionUtil.isNotEmpty(jsjg)) {
//                jsjg.put("zsxmDm", zsxmDm);
//                result.add(jsjg);
//            }
//        });
//        return result;
//    }
//
//    @Override
//    public void saveTaxCalcResult(ZnjsqRequestDTO rq) {
//        JSONObject qqbw = JSONObject.parseObject(rq.getQqbw());
//        List<JSONObject> sfjsjg = qqbw.getObject("znsfjsjg", List.class);
//        List<JSONObject> ywmxxx = qqbw.getObject("ywmxxx", List.class);
//        BigDecimal djxh = getDataBd(rq.getDjxh());
//        // 1.保存税费智能计算结果主表
//        SbSfznjsjgzbDO sfznjsjgzbDO = new SbSfznjsjgzbDO();
//        String sfznjsjgzbuuid = UUIDGenerator.generate("sfznjsjgzbuuid");
//        sfznjsjgzbDO.setSfznjsjgzbuuid(sfznjsjgzbuuid);
//        sfznjsjgzbDO.setDjxh(djxh);
//        sfznjsjgzbDO.setFaglhtuuid(rq.getFaglhtuuid());
//        sfznjsjgzbDO.setFhrq1(DateUtil.parse(qqbw.getString("fhrq")));
//        sfznjsjgzbDO.setHbszDm(qqbw.getString("bz"));
//        Map<String, JSONObject> zsxmYwmx = ywmxxx.stream().collect(Collectors.toMap(e -> e.getString("zsxmDm"), Function.identity()));
//        JSONObject zzsJson = zsxmYwmx.get("10101");
//        JSONObject qysdsJson = zsxmYwmx.get("10104");
//        boolean hadZzs = CollectionUtil.isNotEmpty(zzsJson);
//        boolean hadQysds = CollectionUtil.isNotEmpty(qysdsJson);
//        // 获取增值税税率、企业所得税税率，存在记录则为1，不存在则为0；f1：增值税，f2：企业所得税
//        sfznjsjgzbDO.setZsdkdjqysdsbz(hadQysds ? YsqCommUtil.Y_STR : YsqCommUtil.NOT_STR);
//        sfznjsjgzbDO.setZsdkdjzzsbz(hadZzs ? YsqCommUtil.Y_STR : YsqCommUtil.NOT_STR);
//        sfznjsjgzbDO.setZsjmqycdqysdsbz(!hadZzs ? YsqCommUtil.NOT_STR : 1 == qysdsJson.getInteger("sfcdlx") ? YsqCommUtil.Y_STR : YsqCommUtil.NOT_STR);
//        sfznjsjgzbDO.setZsjmqycdzzsbz(!hadZzs ? YsqCommUtil.NOT_STR : 1 == zzsJson.getInteger("sfcdlx") ? YsqCommUtil.Y_STR : YsqCommUtil.NOT_STR);
//        YsqCommUtil.setCommField(sfznjsjgzbDO, SbSfznjsjgzbDO.class, rq);
//        znjsjgzbMapper.insert(sfznjsjgzbDO);
//        // 2.保存税费智能计算结果明细表
//        String gjhdqDm = qqbw.getString("gjhdqszDm");
//        Map<String, JSONObject> zsxmJsjg = sfjsjg.stream().collect(Collectors.toMap(e -> e.getString("zsxmDm"), Function.identity()));
//        List<SbSfznjsjgmxbDO> jsjgnxs = new ArrayList<>();
//        ywmxxx.stream().forEach(ywmx -> {
//            String zsxmDm = ywmx.getString("zsxmDm");
//            String zspmDm = ywmx.getString("zspmDm");
//            String zszmDm = ywmx.getString("zszmDm");
//            SbSfznjsjgmxbDO jsjgmxbDO = new SbSfznjsjgmxbDO();
//            jsjgmxbDO.setDjxh(djxh);
//            jsjgmxbDO.setSfznjsjgzbuuid(sfznjsjgzbuuid);
//            jsjgmxbDO.setSfznjsjgmxbuuid(UUIDGenerator.generate(zspmDm));
//            jsjgmxbDO.setZsxmDm(zsxmDm);
//            jsjgmxbDO.setZspmDm(zspmDm);
//            jsjgmxbDO.setZszmDm(zszmDm);
//            jsjgmxbDO.setGjhdqszDm(gjhdqDm);
//            jsjgmxbDO.setZsdlfsDm(ywmx.getString("zsdlfsDm"));
//            jsjgmxbDO.setSbsdlxDm(ywmx.getString("sbsdlxDm"));
//            jsjgmxbDO.setKjlxDm(ywmx.getString("kjlxDm"));
//            jsjgmxbDO.setSl1(getDataBd(ywmx.getString(ZnjsqCommConstant.SL_STR)));
//            jsjgmxbDO.setHl1(getDataBd(ywmx.getString(ZnjsqCommConstant.HL_STR)));
//            if (ywmx.containsKey("zsl")) {
//                jsjgmxbDO.setZsl(getDataBd(ywmx.getString("zsl")));
//            }
//            if (ywmx.containsKey("htze")) {
//                jsjgmxbDO.setHtzje(getDataBd(ywmx.getString("htze")));
//            }
//            if (ywmx.containsKey("gqjz")) {
//                jsjgmxbDO.setGqjz(getDataBd(ywmx.getString("gqjz")));
//            }
//            if (ywmx.containsKey("hdlrl")) {
//                jsjgmxbDO.setHdlrl(getDataBd(ywmx.getString("hdlrl")));
//            }
//            if (ywmx.containsKey("bczrgqbl")) {
//                jsjgmxbDO.setBczrdgqbl(getDataBd(ywmx.getString("bczrgqbl")));
//            }
//            if (ywmx.containsKey("cczrqcygqbl")) {
//                jsjgmxbDO.setZrqcygqbl(getDataBd(ywmx.getString("cczrqcygqbl")));
//            }
//            if (ywmx.containsKey(ZnjsqCommConstant.MRJ_STR)) {
//                jsjgmxbDO.setHdlrl(getDataBd(ywmx.getString(ZnjsqCommConstant.MRJ_STR)));
//            }
//            if (zsxmJsjg.containsKey(zsxmDm)) {
//                JSONObject jsjg = zsxmJsjg.get(zsxmDm);
//                // 企业所得税是存应纳税所得税，印花税时存计税金额，其余存计税依据
//                if (jsjg.containsKey(ZnjsqCommConstant.JSYJ_STR) || "10104,10111".contains(zsxmDm)) {
//                    String jsjgKey = "10104".equals(zsxmDm) ? ZnjsqCommConstant.YNSSDE_STR : "10111".equals(zsxmDm) ? ZnjsqCommConstant.JSJE_STR : ZnjsqCommConstant.JSYJ_STR;
//                    jsjgmxbDO.setJsyj(getDataBd(jsjg.getString(jsjgKey)));
//                }
//                // 房产、文化存应纳税所得额，其余存yjse
//                if (jsjg.containsKey(ZnjsqCommConstant.YJSE_STR) || "30217,10110".contains(zsxmDm)) {
//                    jsjgmxbDO.setYjsf(getDataBd(jsjg.getString("30217,10110".contains(zsxmDm) ? ZnjsqCommConstant.YNSSDE_STR : ZnjsqCommConstant.YJSE_STR)));
//                }
//            }
//            jsjgmxbDO.setSbbzt(KjsbJsjgEnum.DQD.getSbjgbz());
//            jsjgmxbDO.setSsxdsyslpzuuid(rq.getSsxdsyslpzuuid());
//            YsqCommUtil.setCommField(jsjgmxbDO, SbSfznjsjgmxbDO.class, rq);
//            jsjgnxs.add(jsjgmxbDO);
//            // znjsjgmxbMapper.insert(jsjgmxbDO);
//            List<JSONObject> gqjzxx = ywmx.getObject("gqjzxx", List.class);
//            List<JSONObject> ajqpjjsmrjxx = ywmx.getObject("ajqpjjsmrjxx", List.class);
//            // 3.保存非居民跨境办税税费智能计算结果明细表附表
//            List<SbFjmkjbsSfznjsjgmxbfbDO> mxbfbList = new ArrayList<>();
//            if (CollectionUtil.isNotEmpty(gqjzxx) || CollectionUtil.isNotEmpty(ajqpjjsmrjxx)) {
//                Integer gqLen = (CollectionUtil.isNotEmpty(gqjzxx) ? gqjzxx.size() : 0);
//                Integer mrjLen = (CollectionUtil.isNotEmpty(ajqpjjsmrjxx) ? ajqpjjsmrjxx.size() : 0);
//                // 3.1保存企业所得税的股权净值
//                for (int i = 0; i < mrjLen + gqLen; i++) {
//                    SbFjmkjbsSfznjsjgmxbfbDO jsjgmxbfbDO = new SbFjmkjbsSfznjsjgmxbfbDO();
//                    jsjgmxbfbDO.setDjxh(djxh);
//                    jsjgmxbfbDO.setUuid(UUIDGenerator.generate("SbFjmkjbsSfznjsjgmxbfbDO"));
//                    jsjgmxbfbDO.setFjmkjbssfznjsjgmxbuuid(jsjgmxbDO.getSfznjsjgmxbuuid());
//                    if (i < gqLen) {
//                        JSONObject mxxx = gqjzxx.get(i);
//                        jsjgmxbfbDO.setHbszDm(mxxx.getString("jjbz"));
//                        jsjgmxbfbDO.setXh(mxxx.getInteger("xh"));
//                        jsjgmxbfbDO.setTzcb(getDataBd(mxxx.getString("tzcb")));
//                        jsjgmxbfbDO.setTzcbzhrmbje(getDataBd(mxxx.getString("tzcbzhrmbje")));
//                        jsjgmxbfbDO.setHshl(getDataBd(mxxx.getString("hshl")));
//                    } else {
//                        JSONObject mxxx = ajqpjjsmrjxx.get(mrjLen + gqLen - i - 1);
//                        jsjgmxbfbDO.setXh(mxxx.getInteger("xh"));
//                        jsjgmxbfbDO.setMrgqbl(getDataBd(mxxx.getString("mrgqbl")));
//                        jsjgmxbfbDO.setMrj(getDataBd(mxxx.getString("mrj")));
//                    }
//                    YsqCommUtil.setCommField(jsjgmxbfbDO, SbFjmkjbsSfznjsjgmxbfbDO.class, rq);
//                    mxbfbList.add(jsjgmxbfbDO);
//                }
//            }
//            if (CollectionUtil.isNotEmpty(mxbfbList)) {
//                Integer batch = znjsjgmxbfbMapper.insertBatchSomeColumn(mxbfbList);
//                log.info("明细表附表插入了{}条", batch);
//            }
//        });
//        if (CollectionUtil.isNotEmpty(jsjgnxs)) {
//            Integer batch = znjsjgmxbMapper.insertBatchSomeColumn(jsjgnxs);
//            log.info("明细表附表插入了{}条", batch);
//        }
//    }
//
//    /**
//     * 数据转换成BigDecimal类型
//     *
//     * @param data
//     * @return
//     */
//    private BigDecimal getDataBd(String data) {
//        return new BigDecimal(data);
//    }
//
//    @Override
//    public void updateSfjsjgmx(ZnjsqRequestDTO rq) {
//        JSONObject qqbw = JSONObject.parseObject(rq.getQqbw());
//        JSONObject update = qqbw.getJSONObject("update");
//        JSONObject condition = qqbw.getJSONObject("condition");
//        try {
//            znjsjgmxbMapper.update((SbSfznjsjgmxbDO) YsqCommUtil.json2Dto(update, SbSfznjsjgmxbDO.class), new UpdateWrapper((SbSfznjsjgmxbDO) YsqCommUtil.json2Dto(condition, SbSfznjsjgmxbDO.class)));
//        } catch (InstantiationException | IntrospectionException | IllegalAccessException e) {
//            log.error("json转SbSfznjsjgmxbDO对象异常：", e);
//            throw new BizNestedException("更新税费计算结果明细表中的数据失败");
//        }
//    }
//
//    /**
//     * 更新方案名称
//     *
//     * @param rq
//     */
//    @Override
//    public void updateScheme(ZnjsqRequestDTO rq) {
//        faxxbMapper.updateNsywpdjg(Stream.of(new String[][]{{"famc", rq.getFamc()}}).collect(Collectors.toMap(e -> e[0], e -> e[1])), rq.getFabh(), rq.getDjxh());
//    }
//
//    @Override
//    public void delScheme(ZnjsqRequestDTO rq) {
//        faxxbMapper.updateNsywpdjg(Stream.of(new String[][]{{"zfbz_1", "Y"}}).collect(Collectors.toMap(e -> e[0], e -> e[1])), rq.getFabh(), rq.getDjxh());
//    }

}
