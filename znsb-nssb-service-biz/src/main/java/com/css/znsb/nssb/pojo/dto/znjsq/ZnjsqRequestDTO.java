package com.css.znsb.nssb.pojo.dto.znjsq;


import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * @version 1.00.00
 * @<NAME_EMAIL>
 * @company 方欣科技有限公司
 * @date 2023/3/16 10:55:51
 * @Class ZnjsqRequestDTO
 * @modify by
 */
@Data
@Builder(builderMethodName = "builder2")
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "智能计算器请求实体")
public class ZnjsqRequestDTO implements Serializable {

    private static final long serialVersionUID = -8672338547424106326L;

    @Schema(description = "登记序号")
    @JsonProperty("Djxh")
    String djxh;

    @Schema(description = "纳税人识别号")
    @JsonProperty("Nsrsbh")
    String nsrsbh;

    @Schema(description = "行政区划数字代码")
    @JsonProperty("XzqhszDm")
    String xzqhszDm;

    @Schema(description = "实际登记序号")
    @JsonProperty("Yddjxh")
    String yddjxh;

    @Schema(description = "非居民身份码")
    @JsonProperty("Fjmsfm")
    String fjmsfm;

    @Schema(description = "题目答案")
    @JsonProperty("Tmda")
    String tmda;

    @Schema(description = "费用类型（二级）代码")
    @JsonProperty("Fylx2jDm")
    String fylx2jDm;

    @Schema(description = "费用类型（一级）代码")
    @JsonProperty("Fylx1jDm")
    String fylx1jDm;

    @Schema(description = "方案编号")
    @JsonProperty("Fabh")
    String fabh;

    @Schema(description = "方案名称")
    @JsonProperty("Famc")
    String famc;

    @Schema(description = "方案名称")
    @JsonProperty("Fasyfw")
    String fasyfw;

    @Schema(description = "合同编号")
    @JsonProperty("Htbh")
    String htbh;

    @Schema(description = "合同编号集合")
    @JsonProperty("HtbhList")
    List<String> htbhList;

    @Schema(description = "合同金额")
    @JsonProperty("Htje")
    Double htje;

    @Schema(description = "非居民跨境办税费用判定逻辑表UUID")
    @JsonProperty("Fjmkjbsfypdljbuuid")
    String fjmkjbsfypdljbuuid;

    @Schema(description = "方案关联合同UUID")
    @JsonProperty("Faglhtuuid")
    String faglhtuuid;

    @Schema(description = "税收协定适用税率配置UUID")
    @JsonProperty("Ssxdsyslpzuuid")
    String ssxdsyslpzuuid;

    @Schema(description = "业务类别")
    @JsonProperty("Ywlb")
    String ywlb;

    @Schema(description = "是否匿名方案")
    @JsonProperty("Sfnmfa")
    String sfnmfa;

    @Schema(description = "减免性质代码")
    @JsonProperty("JmxzDm")
    String jmxzDm;

    @Schema(description = "请求报文")
    @JsonProperty("Qqbw")
    String qqbw;

    @Schema(description = "国家和地区代码")
    @JsonProperty("GjhdqDm")
    String gjhdqDm;

    @Schema(description = "方案绑定合同的解绑时间")
    @JsonProperty("Jbrq")
    String jbrq;

    @Schema(description = "扣缴申报编码")
    @JsonProperty("Kjsbbm")
    String kjsbbm;

    @Schema(description = "有效标志")
    @JsonProperty("Yxbz")
    String yxbz;

    @Schema(description = "问题选项")
    @JsonProperty("Wtxx")
    String wtxx;

    @Schema(description = "保存标志")
    @JsonProperty("onlySave")
    String onlySave;
}
