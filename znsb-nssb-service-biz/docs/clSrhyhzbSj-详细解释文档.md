# `clSrhyhzbSj` 方法详细解释文档

## 概述

<mcfile name="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java"></mcfile> 中的 <mcsymbol name="clSrhyhzbSj" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="79" type="function"></mcsymbol> 方法是收入还原汇总表数据的核心处理入口，负责将明细数据转换为汇总数据并生成 JSON 格式的指标信息。

## 核心工作流程

```
明细数据获取 → 企业分组 → 收入/销项数据生成 → 指标装配 → JSON构建 → 批量保存
```

### 1. 数据获取与分组

- 从 `ZnsbNssbSrmxbMapper` 获取指定期间的明细数据
- 按企业（`nsrsbh`）分组处理，确保数据一致性

### 2. 双口径数据生成

#### 收入数据（sjlx=0）
通过 <mcsymbol name="createRevenueData" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="213" type="function"></mcsymbol> 创建收入记录：
- 设置基础字段：所属期、单据号、纳税人信息、录入者信息
- 标记 `sjlx = "0"`（收入侧）
- 为后续指标装配预留结构

#### 销项数据（sjlx=1）  
通过 <mcsymbol name="createTaxData" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="254" type="function"></mcsymbol> 创建销项记录：
- 设置基础字段：所属期、单据号、纳税人信息、录入者信息
- 标记 `sjlx = "1"`（销项侧）
- 为后续指标装配预留结构

### 3. 指标装配系统

#### 指标装配器调用
<mcsymbol name="applyMetricAssembler" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="584" type="function"></mcsymbol> 方法负责：
- 调用指标字典初始化器获取指标定义
- 调用规则映射器获取装配规则
- 通过装配器将明细数据聚合为指标Map

### 4. JSON构建与回填
<mcsymbol name="buildMetricsJson" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="680" type="function"></mcsymbol> 方法负责：
- 构建 `metrics_json` 字段内容
- 将指标数据回填到DO对象属性中

## 三大核心配置器详解

### 1. 收入还原指标字典数据初始化器

#### 文件位置
<mcfile name="MetricDefInitializer.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/MetricDefInitializer.java"></mcfile>

#### 核心方法
<mcsymbol name="getAllMetricDefinitions" filename="MetricDefInitializer.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/MetricDefInitializer.java" startline="23" type="function"></mcsymbol>

#### 功能说明
负责定义所有收入还原相关的指标元数据，包括：
- **主营业务收入指标**：如成品销售、仓储快递、收派服务等
- **其他业务收入指标**：如股份、设备租金、租赁收入等  
- **增值税应税收入指标**：对应各项收入的增值税应税部分
- **视同销售收入指标**：如加盟商利息、园区租金等
- **汇总指标**：营业收入总额、增值税应税收入汇总等
- **乐企调整凭证指标**：各种税率的调整凭证

#### 指标定义结构
每个指标包含：
```java
ZnsbNssbMetricDefDO metricDef = new ZnsbNssbMetricDefDO();
metricDef.setMetricCode("zycpxs");           // 指标编码
metricDef.setMetricName("主营-成品销售");      // 指标名称  
metricDef.setMetricCategory("主营业务收入");   // 指标分类
metricDef.setSourceRule(sourceRule);         // 来源规则JSON
metricDef.setComputeFormula(formula);        // 计算公式
```

### 2. 指标装配器

#### 文件位置
<mcfile name="SrhyMetricAssembler.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricAssembler.java"></mcfile>

#### 核心方法
<mcsymbol name="assemble" filename="SrhyMetricAssembler.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricAssembler.java" startline="24" type="function"></mcsymbol>

#### 功能说明
负责将明细DTO列表聚合为指标Map：
- 遍历明细数据，应用装配规则进行匹配
- 按指标编码累加金额
- 返回 `Map<String, BigDecimal>` 格式的指标结果

#### 装配规则匹配
通过 `SourceMatchRule` 内部类实现复杂的匹配逻辑：
```java
public static class SourceMatchRule {
    private Set<String> kmdmPrefixes;  // 科目代码前缀集合
    private Set<String> kjfpList;      // 会计分配代码集合  
    private Set<String> taxRates;      // 税率集合
    
    public boolean match(String kmdm, String kjfp, BigDecimal sl) {
        // 执行多维度匹配逻辑
    }
}
```

### 3. 收入还原指标装配规则映射器

#### 文件位置
<mcfile name="SrhyMetricRuleMapper.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricRuleMapper.java"></mcfile>

#### 核心方法
<mcsymbol name="getAllMetricRules" filename="SrhyMetricRuleMapper.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricRuleMapper.java" startline="25" type="function"></mcsymbol>

#### 功能说明
定义指标编码与装配规则的映射关系，包含四大类规则：

##### 主营业务收入装配规则
- `zycpxs`：科目代码600101 + 税率13%
- `zycpxs1`：科目代码600101 + 税率6%
- `zycckd`：科目代码6001060000 + 税率6%
- `zyspfw`：科目代码6001070000 + 税率6%
- `zyjwfy`：科目代码600103
- `zyqhth`：科目代码6001020000 + 会计分配B01 + 税率0%
- 等等...

##### 其他业务收入装配规则
- `qtgf`：科目代码605101/605102/605103 + 税率13%
- `qtsbzj`：科目代码6051040000 + 税率13%
- `qtccfw`：科目代码6051 + 会计分配B03/B11/B12等 + 税率6%
- 等等...

##### 增值税应税收入装配规则
- 复用主营和其他业务收入规则，但指标编码前缀为"zzs"

##### 视同销售收入装配规则  
- `zzsstxssrbdyqzj`：科目代码6601221600/6601221700 + 税率9%
- 乐企调整凭证系列：各种税率的调整规则
- 乐企尾差凭证系列：各种税率的尾差规则

## 新增项目大类和项目小类的配置步骤

### 步骤1：在指标字典初始化器中添加指标定义

#### 1.1 确定新增指标的分类
在 <mcfile name="MetricDefInitializer.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/MetricDefInitializer.java"></mcfile> 中找到对应的方法：
- 主营业务收入 → `getMainBusinessMetrics()`
- 其他业务收入 → `getOtherBusinessMetrics()`
- 增值税应税收入 → `getVatTaxableMetrics()`
- 视同销售收入 → `getDeemedSalesMetrics()`

#### 1.2 添加新指标定义
```java
// 示例：新增主营业务收入-新产品销售
private static List<ZnsbNssbMetricDefDO> getMainBusinessMetrics() {
    List<ZnsbNssbMetricDefDO> metrics = new ArrayList<>();
    
    // ... 现有指标 ...
    
    // 新增：主营-新产品销售
    metrics.add(createMetric(
        "zyxcpxs",                    // 指标编码
        "主营-新产品销售",             // 指标名称
        "主营业务收入",               // 指标分类
        buildSourceRule(
            Arrays.asList("600199"),  // 科目代码前缀
            Collections.emptyList(),  // 会计分配代码
            Arrays.asList("13")       // 税率
        ),
        "sum(se) where kmdm like '600199%' and sl = 0.13"  // 计算公式
    ));
    
    return metrics;
}
```

#### 1.3 在汇总方法中添加引用
在 <mcsymbol name="getAllMetricDefinitions" filename="MetricDefInitializer.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/MetricDefInitializer.java" startline="23" type="function"></mcsymbol> 中确保新指标被包含：
```java
public static List<ZnsbNssbMetricDefDO> getAllMetricDefinitions() {
    List<ZnsbNssbMetricDefDO> allMetrics = new ArrayList<>();
    
    allMetrics.addAll(getMainBusinessMetrics());  // 包含新增的主营指标
    allMetrics.addAll(getOtherBusinessMetrics());
    allMetrics.addAll(getVatTaxableMetrics());
    allMetrics.addAll(getDeemedSalesMetrics());
    allMetrics.addAll(getSummaryMetrics());
    allMetrics.addAll(getLeqiAdjustmentMetrics());
    
    return allMetrics;
}
```

### 步骤2：在规则映射器中添加装配规则

#### 2.1 在对应规则方法中添加映射
在 <mcfile name="SrhyMetricRuleMapper.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricRuleMapper.java"></mcfile> 的相应方法中添加：

```java
private static Map<String, SourceMatchRule> getMainBusinessRules() {
    Map<String, SourceMatchRule> rules = new HashMap<>();
    
    // ... 现有规则 ...
    
    // 新增：主营-新产品销售装配规则
    rules.put("zyxcpxs", new SourceMatchRule(
        Collections.singleton("600199"),      // 科目代码前缀
        Collections.emptySet(),               // 会计分配（空表示不限制）
        Collections.singleton("0.13")         // 税率13%
    ));
    
    return rules;
}
```

#### 2.2 如果是新大类，添加新的规则方法
```java
/**
 * 新业务大类装配规则
 */
private static Map<String, SourceMatchRule> getNewBusinessRules() {
    Map<String, SourceMatchRule> rules = new HashMap<>();
    
    // 新业务-项目小类1
    rules.put("xywxmlx1", new SourceMatchRule(
        Collections.singleton("609001"),      // 专用科目代码
        Collections.emptySet(),               
        Collections.singleton("0.13")         
    ));
    
    // 新业务-项目小类2
    rules.put("xywxmlx2", new SourceMatchRule(
        Collections.singleton("609002"),      
        Collections.emptySet(),               
        Collections.singleton("0.06")         
    ));
    
    return rules;
}
```

#### 2.3 在汇总方法中添加新大类规则
在 <mcsymbol name="getAllMetricRules" filename="SrhyMetricRuleMapper.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricRuleMapper.java" startline="25" type="function"></mcsymbol> 中添加：
```java
public static Map<String, SourceMatchRule> getAllMetricRules() {
    Map<String, SourceMatchRule> ruleMap = new HashMap<>();
    
    ruleMap.putAll(getMainBusinessRules());
    ruleMap.putAll(getOtherBusinessRules());
    ruleMap.putAll(getVatTaxableRules());
    ruleMap.putAll(getDeemedSalesRules());
    ruleMap.putAll(getNewBusinessRules());  // 新增业务大类规则
    
    return ruleMap;
}
```

### 步骤3：更新常量定义（如需要）

#### 3.1 如果使用新的科目代码，在常量类中添加
在 `SrhyConstants` 类中添加新的科目代码常量：
```java
public class SrhyConstants {
    // ... 现有常量 ...
    
    // 新产品销售科目代码
    public static final String KMDM_NEW_PRODUCT = "600199";
    
    // 新业务大类科目代码列表
    public static final List<String> KMDM_NEW_BUSINESS = Arrays.asList(
        "609001", "609002", "609003"
    );
}
```

#### 3.2 在规则映射器中引用常量
```java
rules.put("zyxcpxs", new SourceMatchRule(
    Collections.singleton(SrhyConstants.KMDM_NEW_PRODUCT),  // 使用常量
    Collections.emptySet(),
    Collections.singleton("0.13")
));
```

### 步骤4：更新指标优先级（可选）

在 <mcsymbol name="getMetricPriorities" filename="SrhyMetricRuleMapper.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/metric/SrhyMetricRuleMapper.java" startline="415" type="function"></mcsymbol> 方法中为新指标设置装配优先级：
```java
public static Map<String, Integer> getMetricPriorities() {
    Map<String, Integer> priorities = new HashMap<>();
    
    // ... 现有优先级 ...
    
    // 新增指标优先级
    priorities.put("zyxcpxs", 2);     // 新产品销售明细指标
    priorities.put("xywxmlx1", 2);    // 新业务项目小类1
    priorities.put("xywxmlx2", 2);    // 新业务项目小类2
    
    return priorities;
}
```

### 步骤5：验证与测试

#### 5.1 单元测试
创建测试用例验证新指标的装配逻辑：
```java
@Test
public void testNewMetricAssembly() {
    // 构造测试明细数据
    List<ZnsbNssbSrmxbDTO> testData = Arrays.asList(
        createTestDetail("600199", "0.13", new BigDecimal("10000"))
    );
    
    // 获取装配规则
    Map<String, SourceMatchRule> rules = SrhyMetricRuleMapper.getAllMetricRules();
    
    // 执行装配
    Map<String, BigDecimal> result = SrhyMetricAssembler.assemble(testData, rules);
    
    // 验证结果
    assertEquals(new BigDecimal("10000"), result.get("zyxcpxs"));
}
```

#### 5.2 集成测试
通过 <mcfile name="SrhyController.java" path="znsb-nssb/znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/controller/srhy/SrhyController.java"></mcfile> 触发完整流程测试：
```bash
# 调用处理接口
POST /srhy/clSrhyhzbSj?cs=202312

# 检查新表中的数据
SELECT metrics_json FROM ZNSB_NSSB_SRHYHZB_BASE 
WHERE sszq = 202312 AND sjlx = '0'
```

## 注意事项

### 1. 数据一致性
- 新增指标编码必须唯一，避免与现有指标冲突
- 装配规则的优先级要合理设置，避免重复计算

### 2. 性能考虑  
- 复杂的装配规则会影响性能，建议批量处理时设置合理的批次大小
- 指标过多时考虑分页或异步处理

### 3. 向后兼容
- 修改现有指标时要考虑历史数据的兼容性
- 新增指标不影响现有业务逻辑

### 4. 配置验证
- 部署前通过单元测试验证所有新增配置
- 在测试环境验证完整的数据流程

## 相关文件结构

```
znsb-nssb-service-biz/
├── src/main/java/com/css/znsb/nssb/service/srhy/
│   ├── impl/
│   │   └── ZnsbNssbSrhyhzbBaseServiceImpl.java  # 主处理逻辑
│   └── metric/
│       ├── MetricDefInitializer.java            # 指标字典初始化器
│       ├── SrhyMetricAssembler.java             # 指标装配器
│       └── SrhyMetricRuleMapper.java            # 规则映射器
├── docs/
│   └── clSrhyhzbSj-详细解释文档.md              # 本文档
└── ...
```

通过以上配置步骤，可以灵活地扩展收入还原指标体系，满足不断变化的业务需求。