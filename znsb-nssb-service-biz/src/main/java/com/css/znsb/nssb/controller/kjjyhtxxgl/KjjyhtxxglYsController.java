package com.css.znsb.nssb.controller.kjjyhtxxgl;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.css.znsb.framework.common.util.znsb.GyUtils;
import com.css.znsb.nssb.pojo.dto.kjjyhtxxgl.KjjyhtxxglHtlbxxRequestDTO;
import com.css.znsb.nssb.pojo.vo.kjjyhtxxgl.FwmydxmdwzfswbaRequestVO;
import com.css.znsb.nssb.pojo.vo.kjjyhtxxgl.ResponseVO;
import com.css.znsb.nssb.service.kjjyhtxxgl.IKjjyhtxxglService;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Scanner;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

/**
 * 扣缴交易合同信息管理
 */
@RestController
@RequestMapping("/kjjyhtxxgl/v2")
public class KjjyhtxxglYsController {
    private static final Logger log = LoggerFactory.getLogger(KjjyhtxxglYsController.class);
    /**
     * 业务编码
     */
    private static final String YWBM = "kjjyhtxxgl";
    /**
     * 税务事项代码
     */
    private static final String SWSX_DM = "SXN011005301";
    /**
     * 流程税务事项代码
     */
    private static final String LCSWSX_DM = "LCSXN011005301";
    /**
     * 受理税务事项代码
     */
    private static final String SLSWSX_DM = "SLSXN011005301";
    /**
     * 提交给核心的SID
     */
    private static final String HX_SID = "SWZJ.HXZG.SB.HTXXCJBBC";

    /**
     * 引导页条件查询
     * @param req
     * @return
     */
    @PostMapping({"/describeHtlbxxByPage"})
    public CommonResult<ResponseVO> describeHtlbxxByPage(@RequestBody KjjyhtxxglHtlbxxRequestDTO req) {
        ResponseVO responseVO = new ResponseVO();

        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/kjjyhtxxgl-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());

            responseVO.setBizCode("00");
            responseVO.setBody(mockData);
        } catch (IOException e) {
            log.error("读取mock数据失败", e);
            // 如果读取失败，使用原来的服务调用
            responseVO.setBizCode("00");
        }

        return success(responseVO);
    }

    /**
     *
     * @MethodName: getBabzByXthtbh
     * @Description: 根据系统合同编号判断该合同是否已备案
     * <AUTHOR>
     * @param req
     * @return ServerResponse<ResponseDTO>
     * @date 2023年6月27日 上午11:12:03
     */
    @Operation(summary = "根据系统合同编号判断该合同是否已备案")
    @PostMapping("/getBabzByXthtbh")
    public CommonResult<ResponseVO> getBabzByXthtbh(@RequestBody FwmydxmdwzfswbaRequestVO req) {
        ResponseVO responseVO = new ResponseVO();
        if (GyUtils.isNull(req.getXthtbh())) {
            responseVO.setBizCode("01");
            responseVO.setBizMsg("系统合同编号参数不能为空！");
            return success(responseVO);
        }
//        JSONObject reqVo = new JSONObject();
//        reqVo.put("sid", "dzswj.wbzy.zm.zmFwmydxmdwzfswba.getBabzByXthtbh");
//        reqVo.put("djxh", djxh);
//        reqVo.put("htdjbh", req.getXthtbh());
//        String resStr = j3cxSvcClient.jspForward(reqVo.toJSONString());
//        ResponseDTO responseDTO = YsqCommUtil.buildSuccessResponseDTO(resStr);


        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/getBabzByXthtbh-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());
            responseVO.setBizCode("00");
            responseVO.setBody(mockData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return success(responseVO);
    }

    /**
     * 读取InputStream为字符串 (Java 8兼容版本)
     * @param inputStream 输入流
     * @return 字符串内容
     */
    private String readInputStreamAsString(InputStream inputStream) {
        try (Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name())) {
            scanner.useDelimiter("\\A");
            return scanner.hasNext() ? scanner.next() : "";
        }
    }

}

