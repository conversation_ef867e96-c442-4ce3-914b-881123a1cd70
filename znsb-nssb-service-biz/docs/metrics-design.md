# 指标装配器 + 动态指标 JSON 设计说明书

版本：1.0  
作者：系统重构协作  
更新时间：今日

## 1. 背景与目标
- 历史痛点：新增指标需改表/发版，成本高；“乘率前/后”口径混用难对账；明细与主表口径不一致难追溯。
- 本次目标：
  - 引入“指标装配器”，将多来源明细按统一规则聚合，产出指标 Map（assembledMetrics）。
  - 引入“动态指标 JSON”，统一记录非零指标：主表仅写装配器口径；明细并存“装配器口径 + DO 扫描口径”。
  - 降低新增指标成本：可先通过 JSON 快速上线，不改表；必要时再演进实体字段回填。

## 2. 表结构与实体
- 目标表：znsb_nssb_srhyhzb_base（基础汇总信息表）
- 关键字段（节选）：
  - uuid（主键）、djxh、nsrsbh、nsrmc、ssny、lrzx、yysr、zyywsr、qtywsr、zzsyssr、version
  - metrics_json：动态扩展指标 JSON（本次新增方案核心字段）
- 实体映射：
  - 文件：znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/pojo/domain/srhy/ZnsbNssbSrhyhzbBaseDO.java
  - 字段：@TableField("metrics_json") private String metricsJson;

## 3. 代码结构与关键位置
- Service 实现：
  - 文件：znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbServiceImpl.java
  - 方法：
    1) applyMetricAssembler(...) → Map<String, BigDecimal>
       - 收集当前登记序号+录入中心的明细，加载规则（字典优先，内置兜底），调用装配器聚合，回填主表 DO 字段（存在即写），返回指标 Map。
    2) buildMetricsJson(entity, ssny, djxh, lrzx)
       - 旧版：反射扫描 DO BigDecimal 非零字段，写入 nonZeroIndicators。
    3) buildMetricsJson(entity, ssny, djxh, lrzx, assembledMetrics)
       - 主表使用：优先写 assembledIndicators；为空时降级到 DO 扫描。
    4) buildMetricsJson(entity, ssny, djxh, lrzx, assembledMetrics, includeEntityFields)
       - 明细使用：并存 assembledIndicators（装配器口径）+ entityIndicators（DO 扫描口径）。
- 规则与装配组件：
  - SrhyMetricAssembler：按规则对明细聚合求和，返回 Map<String, BigDecimal>。
  - SrhyMetricRuleMapper：提供内置规则映射（兜底）。
  - MetricDefInitializer：加载字典规则（可扩展为 DB/配置中心热加载）。

## 4. 处理流程（概要）
1) 按 FZ1、FZ1出项科目、FZ2、FZ4、FZ6、FZ7、差异行等分组，收集当前登记序号+录入中心的所有明细。
2) 加载规则：先字典（MetricDefInitializer），为空则内置（SrhyMetricRuleMapper）。
3) 调用 SrhyMetricAssembler.assemble(details, ruleMap) → metricValues。
4) 回填：对 metricValues 中每个指标，BeanUtils.setProperty 回填到主表 DO（字段存在则写，不存在跳过）。
5) 写 metrics_json：
   - 主表：buildMetricsJson(entity, ..., assembledMetrics)（优先装配器 → 空降级 DO 扫描）。
   - 明细：buildMetricsJson(entity, ..., assembledMetrics, true)（并存两套口径）。

## 5. 指标装配器设计
- 规则优先级：字典规则（可运维配置/可扩展热加载） > 内置规则（代码内映射）。
- 聚合策略：
  - 对每个指标编码 metricCode，匹配相应明细，按规则过滤、分组、求和；
  - 使用 result.merge(key, value, BigDecimal::add) 聚合避免覆盖与 NPE；
  - 输出 Map<String, BigDecimal>，键为指标编码（如 A001），值为汇总额。
- 回填策略：
  - 对返回 Map 中每个键，尝试设入主表 DO 同名字段；字段不存在则忽略，不抛错；
  - 即便未新增实体列，也可先通过 JSON 完整落地。

## 6. 动态指标 JSON 结构
- 根节点：
  - meta：{ djxh, ssny, lrzx, schemaVersion, ts }
  - 指标：按口径不同差异化
- 主表口径：
  - 优先写 assembledIndicators（装配器口径，现已统一按“全量输出”策略：指标清单来源于 MetricDefInitializer.getAllMetricDefinitions()，对装配器未命中的指标默认写入字符串 "0"，数值均使用去尾零的字符串表示 toPlainString）；
  - 当装配器 Map 为空时，降级写 nonZeroIndicators（反射扫描 DO 非零）。
- 明细口径：
  - 并存两套：
    - assembledIndicators：装配器口径（已统一为“全量输出”，未命中默认写入字符串 "0"）。
    - entityIndicators：DO 扫描口径（仅写非零，去尾零字符串）。

## 7. 健壮性与降级
- 规则加载失败：warn 日志 → 使用内置规则兜底。
- 装配结果为空：主表降级 DO 扫描；明细仅保留 entityIndicators。
- DO 字段不存在：回填忽略异常，不中断。
- JSON 构建异常：warn 日志，不影响主流程。

## 8. 新增“指标/字段”的操作步骤
### A. 仅通过 JSON 快速上线（推荐首选）
1) 新增规则定义：
   - 优先在 MetricDefInitializer 中新增指标定义（或接入 DB/配置中心）；
   - 如短期无法改字典，则先在 SrhyMetricRuleMapper 增加内置规则映射。
2) 验证：跑一条含该口径的明细，确认 applyMetricAssembler 返回的 Map 中出现新指标编码。
3) JSON 自动落地：
   - 主表 metrics_json：assembledIndicators 中包含新指标；若装配器未命中则降级 DO 扫描。
   - 明细 metrics_json：assembledIndicators 中包含新指标。
（无需改表/改 DO）。

### B. 需要实体字段回填（供 SQL 报表/接口直接使用）
1) 数据库加列（示例）：
   - ALTER TABLE znsb_nssb_srhyhzb_base ADD COLUMN a123 DECIMAL(18,2) DEFAULT 0 COMMENT '指标A123';
2) DO 新增字段：
   - 在对应 DO 中新增 @TableField("a123") private BigDecimal a123;
3) 验证回填：
   - 执行流程后，applyMetricAssembler 会将 A123 回填到 DO 字段 a123。
4) JSON 仍包含该指标：主表 assembledIndicators 或降级 nonZeroIndicators；明细并存两套口径均可查看。

### C. 仅 DO 字段新增（无装配器规则）
- 主表不会经装配器写入；明细 entityIndicators 会通过 DO 扫描写入；建议补充装配规则以统一口径。

## 9. 测试与验收
- 单测：
  - SrhyMetricAssembler：规则匹配、过滤分组、聚合精度、空/异常降级；
  - ServiceImpl：三种 buildMetricsJson 重载的结构正确性、元信息完整性；
- 集成：
  - 构造典型 FZ1/FZ2…数据，验证主表/明细 JSON；
  - 新增指标从规则到 JSON/DO 回填的全链路；
- 数据验收：
  - 比对主表 assembledIndicators 与 DO 回填字段一致性；
  - 比对明细 assembledIndicators 与 entityIndicators 的差异（“乘率前/后”）。

## 10. 可扩展建议
- 配置开关：srhy.metrics.json.mode = assembledOnly | entityOnly | both（灰度选择不同重载）。
- 规则热加载：MetricDefInitializer 支持 DB/Redis，增量刷新与版本化。
- 扫描排除清单：抽取常量/配置化，减少重复与维护成本。

---

如需落地“配置开关/单测样例/排除清单常量化”，可继续告知，我将直接补齐实现与测试用例。

## 11. 迁移说明与注意事项

### 11.1 语义变化（重要）
- 旧描述：assembledIndicators 仅保留非零指标，缺失指标不落 JSON。
- 新实现：assembledIndicators 采用“全量输出”策略，指标清单来自字典定义，对装配器未命中的指标默认写入字符串 "0"（去尾零格式）。
- 影响评估：
  - 优点：前后端/报表在使用 JSON 时无需再进行“缺省补零”，可直接遍历全量指标做对账；
  - 注意：如有依赖“缺失即不展示”的前端/报表，需要在读取时过滤值为 "0" 的指标。

### 11.2 解析优先级与兼容策略
- 解析顺序：assembledIndicators → entityIndicators → nonZeroIndicators（最高优先级）。
- 目的：保证兼容旧数据/旧逻辑的同时，避免“全量 0 值”覆盖真实非零值。
- 参考实现：
  - 文件：<mcfile name="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java"></mcfile>
  - 方法：
    - <mcsymbol name="parseMetricsJsonToVO" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="1008" type="function"></mcsymbol>
    - <mcsymbol name="processIndicators" filename="ZnsbNssbSrhyhzbBaseServiceImpl.java" path="znsb-nssb-service-biz/src/main/java/com/css/znsb/nssb/service/srhy/impl/ZnsbNssbSrhyhzbBaseServiceImpl.java" startline="1039" type="function"></mcsymbol>

### 11.3 增量上线策略（建议）
- 灰度开关：srhy.metrics.json.mode = assembledOnly | entityOnly | both（按环境与阶段切换）。
- 双写观察：上线初期同时写 assembledIndicators 与 entityIndicators，校验两套口径差异；
- 审核点：
  - 核对 assembledIndicators 与 DO 字段回填一致性；
  - 比对明细口径（乘率前/后）的期望差异；
  - 异常规则/空规则的降级路径是否符合预期（内置规则兜底）。

### 11.4 数据迁移与验收
- 历史数据不强制迁移：旧 JSON 可被解析优雅降级；
- 有需要时可补写“全量 0 填充”的离线任务，便于报表统一；
- 验收清单：
  - JSON 元信息完整（meta.djsx/ssny/lrzx/schemaVersion/ts）；
  - assembledIndicators 全量且键名与字典一致；
  - entityIndicators 与 nonZeroIndicators 仅包含非零；
  - 解析到 VO 后字段覆盖顺序正确，无 0 值覆盖问题。

### 11.5 常见问题
- Q：为什么要把未命中的指标写 "0"？
  - A：便于统一对账与报表透视，避免在消费端各自补零造成口径不一。
- Q：会不会导致 JSON 变大、性能下降？
  - A：指标总量可控（以字典为准）；字符串 "0" 占用极小，配合批处理策略影响可忽略，详见性能建议。

## 12. 性能与线程池参数建议

### 12.1 批处理与事务边界
- 建议按登记序号/纳税人分批：每批 200~500 条，单批开启事务，避免长事务占用连接与锁。
- 对于大期次重算，优先使用离线任务（如 XXL-Job）串行分片执行，减少 DB 压力。

### 12.2 线程池（参考值，按环境调优）
- CPU 密集（装配与 JSON 构建）：核心线程数 = CPU 核心数，最大 = 2x 核心，队列 1_000，拒绝策略 CallerRuns。
- I/O 密集（DB 读写）：核心 = 核心数，最大 = 4x 核心，队列 5_000，注意与数据源连接池大小匹配（最大并发不应超过连接池）。
- 避免在单事务内并发过多任务，优先“批内串行 + 批间并行”。

### 12.3 数据访问
- 只查所需列，避免 SELECT *；
- MyBatis/JDBC：设置合理 fetchSize（如 500~1_000）与批量提交 batchExecutor；
- 写入使用批量 insert/update，减少往返次数。

### 12.4 内存与序列化
- BigDecimal 统一 toPlainString 去尾零，减少字符串体积；
- 估算内存占用：单条 JSON ~ 数 KB 量级，按批控制在数十 MB 以内；
- 复用对象与缓冲区，避免在热点路径上频繁创建临时对象。

### 12.5 监控与限流
- 接入 SkyWalking/日志采样，关注 P95/P99 延迟与 GC 情况；
- 网关/服务层结合 Sentinel 做限流与熔断，防止重算风暴；
- 关键节点埋点：规则加载、装配耗时、JSON 构建与序列化、DB 写入。

### 12.6 回滚与幂等
- 批处理失败可按登记序号重试；
- 幂等键建议采用（djxh, ssny, lrzx）组合，避免重复写；
- JSON 写入前进行最小化变更检测（内容一致则跳过更新）。