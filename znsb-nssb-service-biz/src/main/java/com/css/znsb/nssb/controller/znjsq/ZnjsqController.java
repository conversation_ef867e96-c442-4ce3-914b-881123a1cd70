package com.css.znsb.nssb.controller.znjsq;

import com.css.znsb.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSONObject;
import com.css.znsb.nssb.pojo.dto.znjsq.KjqsysdbgRequestDTO;
import com.css.znsb.nssb.pojo.dto.znjsq.ZnsbDTO;
import com.css.znsb.nssb.pojo.dto.znjsq.ZnjsqRequestDTO;
//import com.css.znsb.nssb.service.znjsq.IZnjsqService;
import com.css.znsb.nssb.pojo.vo.kjjyhtxxgl.ResponseVO;
import com.css.znsb.nssb.service.znjsq.IZnjsqService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

import static com.css.znsb.framework.common.pojo.CommonResult.success;

/**
 * 智能决策控制器
 * 提供智能决策相关的API接口
 *
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@RestController
@RequestMapping("/znjsq/v1")
@RequiredArgsConstructor
public class ZnjsqController {

    private final IZnjsqService znjsqSvc;


    /**
     * 初始化税纳税义务判断方案
     * 从resource文件中读取JSON数据并转换为List<JSONObject>，避免转义问题
     *
     * @param rq 请求参数
     * @return 返回征费费用类型数据列表
     */
    @PostMapping("/initNsywpdfab")
    @ResponseBody
    public CommonResult<List<JSONObject>> initNsywpdfa(@RequestBody ZnjsqRequestDTO rq) {
        return CommonResult.success(znjsqSvc.initNsywpdfa(rq));
    }


    /**
     * 获取征费费用类型数据
     * 从resource文件中读取JSON数据并转换为List<JSONObject>，避免转义问题
     *
     * @param rq 请求参数
     * @return 返回征费费用类型数据列表
     */
    @PostMapping("/getZffylxData")
    @ResponseBody
    public CommonResult<List<JSONObject>> getZffylxData(@RequestBody ZnjsqRequestDTO rq) {
        return CommonResult.success(znjsqSvc.getZffylxData(rq));
//        // 使用通用方法转换resource为List<JSONObject>
//        List<JSONObject> res = convertResourceToJSONObjectList("mock/getZffylxData-mock-data.json");
//
//        return success(res);
    }

    /**
     * 获取方案数据
     *
     * @param rq 请求参数
     * @return 返回方案数据
     */
    @PostMapping("/getSchemes")
    @ResponseBody
    public CommonResult<List<JSONObject>> getSchemes(@RequestBody ZnjsqRequestDTO rq) {
        return CommonResult.success(znjsqSvc.getSchemes(rq));
    }

    /**
     * 获取所有问题答案
     * 从resource文件中读取JSON数据并直接解析，避免转义问题
     *
     * @param rq 请求参数
     * @return 返回问题答案的JSON对象
     */
    @PostMapping("/getQuestions")
    @ResponseBody
    public CommonResult<JSONObject> getQuestions(@RequestBody ZnjsqRequestDTO rq) {
        return CommonResult.success(znjsqSvc.getQuestions(rq));
//        JSONObject res = new JSONObject();
//        try {
//            // 读取mock数据
//            ClassPathResource resource = new ClassPathResource("mock/getQuestions-mock-data.json");
//            String mockData = readInputStreamAsString(resource.getInputStream());
//            // 直接解析JSON字符串，避免转义问题
//            res = JSONObject.parseObject(mockData);
//        } catch (IOException e) {
//            log.error("读取mock数据失败", e);
//        }
//
//        return success(res);
    }

    /**
     * 根据问题判断纳税人义务
     *
     * @param rq {
     *           “Tmda”：“A01_B01_C01”,"Fylx1jDm":"100","Fylx2jDm":"200"
     *           }
     * @return
     */
    @PostMapping("/judgeDuty")
    public CommonResult<ResponseVO> judgeDuty(@RequestBody ZnjsqRequestDTO rq) {
        ResponseVO respon = new ResponseVO();
        try {
            JSONObject object = znjsqSvc.judgeDuty(rq);

//            // 读取mock数据
//            ClassPathResource resource = new ClassPathResource("mock/judgeDuty-mock-data.json");
//            String mockData = readInputStreamAsString(resource.getInputStream());

            respon.setBizCode("00");
            respon.setBody(object.toJSONString());
        } catch (Exception e) {
            log.error("获取纳税义务异常：", e);
        }
        return success(respon);
    }


    /**
     * 进入税费智能计算业务，获取税收协定信息
     *
     * @param rq {“JmxzDm”：“2112321”,"Zffylxywtxxdauuid":"xx12"}
     * @return
     */
    @PostMapping("/getTaxTreaty")
    @ResponseBody
    public CommonResult<JSONObject> getTaxTreaty(@RequestBody ZnjsqRequestDTO rq) {
//        return success(znjsqSvc.getTaxTreaty(rq));
        JSONObject res = new JSONObject();
        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/getTaxTreaty-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());
            // 直接解析JSON字符串，避免转义问题
            res = JSONObject.parseObject(mockData);
        } catch (IOException e) {
            log.error("读取mock数据失败", e);
        }

        return success(res);
    }

    /**
     * 选择问题，判断纳税人义务后，保存方案
     *
     * @param rq {
     *           "Htbh":"1111",
     *           "Famc":"xxxxxx",
     *           "Zffylxywtxxdauuid":"xx12",
     *           "Sfnmfa":"Y"
     *           }
     * @return 返回成功或失败即可
     */
    @PostMapping("/saveSchemes")
    public CommonResult<ResponseVO> saveSchemes(@RequestBody ZnjsqRequestDTO rq) {
        return success(znjsqSvc.saveSchemes(rq));
    }


    /**
     * 查询代扣代缴文化事业建设费的减征比例和中央比例
     *
     * @param req 请求参数
     * @return
     */
    @PostMapping("/cxWhsyjsfJzbl")
    public CommonResult<ResponseVO> cxWhsyjsfJzbl(@RequestBody ZnsbDTO req) {
        ResponseVO responseVO = new ResponseVO();
//        if (StrUtil.isEmpty(req.getDjxh()) && null != req.getNsrSessionInfo()) {
//            req.setDjxh(req.getNsrSessionInfo().getDjxh());
//            req.setNsrsbh(req.getNsrSessionInfo().getNsrsbh());
//            req.setSwjgDm(req.getNsrSessionInfo().getSwjgDm());
//
//        }
//        if(StringUtils.isEmpty(req.getDjxh())){
//            responseVO.setBizCode(YsqCommUtil.ERROR_BIZCODE);
//            responseVO.setBizMsg("登记序号为空！");
//            responseVO.setBody("登记序号为空！");
//            return success(responseVO);
//        }
//
//        req.set("sid", "dzswj.ywzz.sb.znsb.cxWhsyjsfJzbl");
//        ResponseVO blRes= qysdsSbService.sbservice(req);
//        responseVO.setBizCode(YsqCommUtil.SUCCESS_BIZCODE);
//        responseVO.setBody(blRes.getBody());

        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/cxWhsyjsfJzbl-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());

            responseVO.setBizCode("00");
            responseVO.setBody(mockData);
        } catch (Exception e) {
            log.error("获取纳税义务异常：", e);
        }
        return success(responseVO);

    }

    /**
     * 根据登记序号和系统合同编号查询合同信息
     *
     * @param req 请求参数
     * @return
     */
    @PostMapping("/cxHtxx")
    public CommonResult<ResponseVO> cxHtxx(@RequestBody ZnsbDTO req) {
        ResponseVO responseVO = new ResponseVO();
        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/cxHtxx-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());

            responseVO.setBizCode("00");
            responseVO.setBody(mockData);
        } catch (Exception e) {
            log.error("获取纳税义务异常：", e);
        }
        return success(responseVO);

    }

    /**
     * 根据纳税人识别号查询纳税人信息
     * @param req 请求vo
     * @return 标准格式返回
     */
    //@PostMapping("/cxNsrxxByNsrsbh")
    @PostMapping("/cxFjmsfxx")
    public CommonResult<ResponseVO> cxFjmsfxx(@RequestBody KjqsysdbgRequestDTO req) {
        ResponseVO responseVO = new ResponseVO();
        JSONObject res = new JSONObject();
        try {
            // 读取mock数据
            ClassPathResource resource = new ClassPathResource("mock/cxFjmsfxx-mock-data.json");
            String mockData = readInputStreamAsString(resource.getInputStream());
            res = JSONObject.parseObject(mockData);
            responseVO.setBizCode("00");
            responseVO.setBody(mockData);
        } catch (Exception e) {
            log.error("获取纳税义务异常：", e);
        }
        return success(responseVO);
    }

    /**
     * 将InputStream转换为字符串
     * 使用UTF-8编码读取输入流内容
     *
     * @param inputStream 输入流
     * @return 字符串内容
     */
    private String readInputStreamAsString(InputStream inputStream) {
        try (Scanner scanner = new Scanner(inputStream, StandardCharsets.UTF_8.name())) {
            // 使用正则表达式\A匹配整个输入流
            scanner.useDelimiter("\\A");
            return scanner.hasNext() ? scanner.next() : "";
        }
    }

    /**
     * 通用方法：将resource转换为List<JSONObject>
     * 从指定的resource路径读取JSON数据，解析并转换为JSONObject列表，避免转义问题
     *
     * @param resourcePath resource文件路径
     * @return JSONObject列表
     */
    private List<JSONObject> convertResourceToJSONObjectList(String resourcePath) {
        List<JSONObject> result = new ArrayList<>();
        try {
            // 1. 创建ClassPathResource对象
            ClassPathResource resource = new ClassPathResource(resourcePath);
            
            // 2. 将resource的InputStream转换为字符串
            String jsonData = readInputStreamAsString(resource.getInputStream());
            
            // 3. 直接解析JSON字符串为JSONObject，避免转义
            JSONObject jsonObject = JSONObject.parseObject(jsonData);
            
            // 4. 从JSONObject中提取data字段的列表数据
            Object dataObj = jsonObject.get("data");
            if (dataObj instanceof List) {
                List<Object> dataList = (List<Object>) dataObj;
                
                // 5. 直接将List中的每个元素转换为JSONObject，避免转义
                for (Object item : dataList) {
                    if (item instanceof Map) {
                        result.add(new JSONObject((Map<String, Object>) item));
                    } else if (item instanceof JSONObject) {
                        result.add((JSONObject) item);
                    }
                }
            }
        } catch (IOException e) {
            log.error("读取resource文件失败: {}", resourcePath, e);
        } catch (Exception e) {
            log.error("转换resource为JSONObject列表失败: {}", resourcePath, e);
        }
        
        return result;
    }
}
