package com.css.znsb.nssb.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *  税收协定待遇适用情况的提示信息
 * </pre>
 *
 * @version 1.00.00
 * @autor wangchao01
 * @date 2023/3/18 11:31:52
 * @Class SssyhtsConstant
 * @modify by
 */
public class ZnjsqCommConstant {

    // 配置表的缓存定义名称
    /**
     * 税收协定适用税率配置表
     */
    public static final String SSXD_CACHE_NAME = "CS_SB_FJMKJBS_SSXDSYSLPZB";
    /**
     * 支付费用类型代码表
     */
    public static final String FYLX_CACHE_NAME = "CS_SB_FJMKJBS_FYGXLXB";
    /**
     * 题库信息
     */
    public static final String TKXX_CACHE_NAME = "CS_SB_FJMKJBS_FYLXYTMGXB";
    /**
     * 支付费用类型与问题选项答案
     */
    public static final String FYLXTMDA_CACHE_NAME = "cs_sb_zffylxywtxxda";
    /**
     * 题目答案信息
     */
    public static final String TMDAXX_CACHE_NAME = "CS_SB_FJMKJBS_NSYWPDTKB";
    /**
     * 业务税收减免等信息配置表
     */
    public static final String YWSSJMXX_CACHE_NAME = "CS_SB_FJMKJBS_FYPDLJB";
    public static final String GLB_ZSPM_CACHE_NAME = "cs_gy_glb_zspm";
    public static final String GLB_ZSZM_CACHE_NAME = "cs_gy_glb_zszm";
    public static final String ZSPM_CACHE_NAME = "dm_gy_zspm";
    public static final String ZSZM_CACHE_NAME = "dm_gy_zszm";
    /**
     * 申报所得类型代码
     */
    public static final String SBSDLX_CACHE_NAME = "dm_sb_sbsdlx";
    /**
     * 扣缴类型代码
     */
    public static final String KJLX_CACHE_NAME = "dm_sb_kjlx";

    /**
     * 税收优惠类型
     */
    public static final String SSYHLX_CACHE_NAME = "DM_SB_FJMKJBSSSYHLX";
    /**
     * 税收协定条款类别代码
     */
    public static final String SSXDTK_CACHE_NAME = "DM_YH_SYSSXDTK";
    /**
     * 优惠政策与税种配置
     */
    public static final String YHZC_CACHE_NAME = "CS_SB_FJMKJBS_YHZCYSZPZB";
    /**
     * 税收协定代码表缓存配置
     */
    public static final String SSXDDM_CACHE_NAME = "DM_GY_SSXD";
    public static final String SSJMXZ_CACHE_NAME = "DM_GY_SSJMXZ";


    // 选择享受税收协定待遇时的提示语
    /**
     * 选择的“适用情况”对应的税率<10%
     */
    public static final String TIP_1 = "需在申报时填写《非居民纳税人享受协定待遇信息报告表》，请确认非居民企业符合享受协定待遇条件，并提醒其留存相关资料备查（详见国家税务总局2019年第35号公告）。";
    /**
     * 若“适用情况”对应的税率≥10%
     */
    public static final String TIP_2 = "该协定待遇不低于国内法规定税率，将按照10%税率为您计算企业所得税。";
    // 企业所得税
    /**
     * 用户可同时选择享受国内税收优惠和税收协定待遇，根据附件《企业所得税税收优惠与协定待遇适用规则》输出结果。当同时勾选国内税收优惠（除递延纳税外）与协定待遇时，输出结果均为国内法免税
     */
    public static final String TIP_3 = "您勾选享受的国内税收优惠可免征企业所得税，无需重复享受协定待遇。";
    /**
     * 选择递延纳税时，若输出结果为协定不征税，则无需申报递延纳税，显示提示信息
     */
    public static final String TIP_4 = "您勾选享受的协定待遇为不征企业所得税，无需申报享受再投资递延纳税政策，请在申报时填写《非居民纳税人享受协定待遇信息报告表》，并提醒非居民企业留存相关资料备查（详见国家税务总局2019年第35号公告）。";
    /**
     * 选择递延纳税且协定税率<10%时，仍可申报享受协定待遇，即需在扣缴企业所得税报告表中同时填报两张附表
     */
    public static final String TIP_5 = "您同时勾选了再投资递延纳税和协定待遇，本次应纳所得税额为0，需在申报时填写《境外投资者递延缴纳预提所得税报告》和《非居民纳税人享受协定待遇信息报告表》，请确认非居民企业符合享受优惠条件，并提醒其留存相关资料备查（详见财税〔2018〕102号、国家税务总局2019年第35号公告）。";

    /**
     *  业务类别：扣缴申报
     */
    public static final String YWLB_KJSB = "kjsb";
    /**
     * 非居民扣缴办税
     */
    public static final String YWLB_FJMKJBS = "fjmkjbs";

    // 税费计算时使用的金额的字符串
    /**
     * 合同金额
     */
    public static final String HTJE_STR = "htje";
    /**
     * 计税价款
     */
    public static final String JSJK_STR = "jsjk";
    /**
     * 汇率
     */
    public static final String HL_STR = "hl";
    /**
     * 税率
     */
    public static final String SL_STR = "sl";

    /**
     * 计税依据
     */
    public static final String JSYJ_STR = "jsyj";
    /**
     * 计税金额
     */
    public static final String JSJE_STR = "jsje";

    /**
     * 应交税额
     */
    public static final String YJSE_STR = "yjse";
    /**
     * 应纳税所得额
     */
    public static final String YNSSDE_STR = "ynssde";


    /**
     * 卖出价
     */
    public static final String MCJ_STR = "mcj";
    /**
     * 买入价
     */
    public static final String MRJ_STR = "mrj";
    /**
     * 支付金额
     */
    public static final String ZFJE_STR = "zfje";
    /**
     * 申报收入金额
     */
    public static final String SBSRJE_STR = "sbsrje";


    /**
     * 业务名称与zsxm对照
     */
    public static final Map<String, String> YWBM_ZSXM = new HashMap<>();

    /**
     * 有些支付费用类型下的问题答案，需要提示请重新选择费用类型
     */
    public static final List<String> ZFFY_DA_NSYW_TIP_LIST = new ArrayList<>();

    static {
        YWBM_ZSXM.put("10104", "代扣代缴企业所得税");
        YWBM_ZSXM.put("10111", "印花税");
        YWBM_ZSXM.put("10101", "代扣代缴增值税");

        ZFFY_DA_NSYW_TIP_LIST.add("003003_B11");
        ZFFY_DA_NSYW_TIP_LIST.add("003004_B13");
        ZFFY_DA_NSYW_TIP_LIST.add("004001_B15");
        ZFFY_DA_NSYW_TIP_LIST.add("005001_B20");
        ZFFY_DA_NSYW_TIP_LIST.add("005003_B23");
        ZFFY_DA_NSYW_TIP_LIST.add("007001_B25");
        ZFFY_DA_NSYW_TIP_LIST.add("012001_B38");
        ZFFY_DA_NSYW_TIP_LIST.add("014001_B40");
        ZFFY_DA_NSYW_TIP_LIST.add("015001_C41");
        ZFFY_DA_NSYW_TIP_LIST.add("016001_B43");
        ZFFY_DA_NSYW_TIP_LIST.add("018001_C46");
        ZFFY_DA_NSYW_TIP_LIST.add("019003_B47");
        ZFFY_DA_NSYW_TIP_LIST.add("021001_C49");
        ZFFY_DA_NSYW_TIP_LIST.add("022001_B50");

    }



}
